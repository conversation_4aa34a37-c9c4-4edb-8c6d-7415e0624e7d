package config

import (
	"fmt"
	"os"
	"sync"
	"time"

	"go.uber.org/zap"
)

// ConfigAdapter 配置适配器
// 提供向后兼容的接口，确保现有代码能够无缝使用新的统一配置系统
type ConfigAdapter struct {
	manager *ConfigManager
	logger  *zap.Logger
	mu      sync.RWMutex
}

var (
	globalAdapter *ConfigAdapter
	adapterOnce   sync.Once
)

// GetConfigAdapter 获取全局配置适配器
func GetConfigAdapter() *ConfigAdapter {
	adapterOnce.Do(func() {
		logger, _ := zap.NewProduction()
		globalAdapter = NewConfigAdapter(GetConfigManager(), logger)
	})
	return globalAdapter
}

// NewConfigAdapter 创建配置适配器
func NewConfigAdapter(manager *ConfigManager, logger *zap.Logger) *ConfigAdapter {
	return &ConfigAdapter{
		manager: manager,
		logger:  logger,
	}
}

// GetLegacyConfig 获取传统格式的配置（向后兼容）
func (ca *ConfigAdapter) GetLegacyConfig() (*Config, error) {
	ca.mu.RLock()
	defer ca.mu.RUnlock()

	// 构建传统格式的配置结构
	config := &Config{
		Server: ServerConfig{
			Port:           ca.manager.GetInt("server.port"),
			ReadTimeout:    ca.manager.GetInt("server.read_timeout"),
			WriteTimeout:   ca.manager.GetInt("server.write_timeout"),
			IdleTimeout:    ca.manager.GetInt("server.idle_timeout"),
			MaxHeaderBytes: ca.manager.GetInt("server.max_header_bytes"),
		},
		Database: DatabaseConfig{
			ConnectionString: ca.manager.GetString("database.connection_string"),
			MaxOpenConns:     ca.manager.GetInt("database.max_open_conns"),
			MaxIdleConns:     ca.manager.GetInt("database.max_idle_conns"),
			ConnMaxLifetime:  ca.manager.GetInt("database.conn_max_lifetime"),
			ConnMaxIdleTime:  ca.manager.GetInt("database.conn_max_idle_time"),
		},
		// 🔄 添加回调数据库配置映射
		CallbackDatabase: DatabaseConfig{
			ConnectionString: ca.manager.GetString("callback_database.connection_string"),
			MaxOpenConns:     ca.manager.GetInt("callback_database.max_open_conns"),
			MaxIdleConns:     ca.manager.GetInt("callback_database.max_idle_conns"),
			ConnMaxLifetime:  ca.manager.GetInt("callback_database.conn_max_lifetime"),
			ConnMaxIdleTime:  ca.manager.GetInt("callback_database.conn_max_idle_time"),
		},
		Redis: RedisConfig{
			ConnectionString: ca.manager.GetString("redis.connection_string"),
			DB:               ca.manager.GetInt("redis.db"),
			PoolSize:         ca.manager.GetInt("redis.pool_size"),
			MinIdleConns:     ca.manager.GetInt("redis.min_idle_conns"),
			MaxConnAge:       ca.manager.GetInt("redis.max_conn_age"),
			PoolTimeout:      ca.manager.GetInt("redis.pool_timeout"),
			IdleTimeout:      ca.manager.GetInt("redis.idle_timeout"),
		},
		Auth: AuthConfig{
			TokenExpirySeconds: ca.manager.GetInt("auth.token_expiry_seconds"),
			Issuer:             ca.manager.GetString("auth.issuer"),
			Audience:           ca.manager.GetString("auth.audience"),
			PrivateKeyPath:     ca.manager.GetString("auth.private_key_path"),
			PublicKeyPath:      ca.manager.GetString("auth.public_key_path"),
		},
		Security: SecurityConfig{
			Signature: SignatureConfig{
				Enabled:   ca.manager.GetBool("security.signature.enabled"),
				SkipPaths: ca.manager.GetStringSlice("security.signature.skip_paths"),
			},
		},
		Performance: PerformanceConfig{
			// 这里可以根据需要添加性能配置的映射
		},
		Providers: make(map[string]interface{}),
	}

	// 映射供应商配置
	config.Providers["kuaidi100"] = map[string]interface{}{
		"name":     ca.manager.GetString("providers.kuaidi100.name"),
		"api_key":  ca.manager.GetString("providers.kuaidi100.api_key"),
		"secret":   ca.manager.GetString("providers.kuaidi100.secret"),
		"customer": ca.manager.GetString("providers.kuaidi100.customer"),
		"base_url": ca.manager.GetString("providers.kuaidi100.base_url"),
		"timeout":  ca.manager.GetDuration("providers.kuaidi100.timeout").Seconds(),
		// express_codes 已迁移到数据库，通过ExpressMappingService获取
	}

	config.Providers["yida"] = map[string]interface{}{
		"username":    ca.manager.GetString("providers.yida.username"),
		"private_key": ca.manager.GetString("providers.yida.private_key"),
		"enabled":     ca.manager.GetBool("providers.yida.enabled"),
		"base_url":    ca.manager.GetString("providers.yida.base_url"),
		"timeout":     ca.manager.GetDuration("providers.yida.timeout").Seconds(),
	}

	config.Providers["yuntong"] = map[string]interface{}{
		"business_id":   ca.manager.GetString("providers.yuntong.business_id"),
		"api_key":       ca.manager.GetString("providers.yuntong.api_key"),
		"e_business_id": ca.manager.GetString("providers.yuntong.e_business_id"),
		"secret_key":    ca.manager.GetString("providers.yuntong.secret_key"),
		"enabled":       ca.manager.GetBool("providers.yuntong.enabled"),
		"base_url":      ca.manager.GetString("providers.yuntong.base_url"),
		"timeout":       ca.manager.GetDuration("providers.yuntong.timeout").Seconds(),
	}

	return config, nil
}

// GetProviderConfigLegacy 获取传统格式的供应商配置
func (ca *ConfigAdapter) GetProviderConfigLegacy(provider string) (*ProviderConfig, bool) {
	ca.mu.RLock()
	defer ca.mu.RUnlock()

	switch provider {
	case "kuaidi100":
		if !ca.manager.GetBool("providers.kuaidi100.enabled") {
			return nil, false
		}

		config := &ProviderConfig{
			Name:    "kuaidi100",
			BaseURL: ca.manager.GetString("providers.kuaidi100.base_url"),
			Timeout: int(ca.manager.GetDuration("providers.kuaidi100.timeout").Seconds()),
		}

		// 从统一配置中获取详细配置
		if endpoints := ca.getMapStringString("providers.kuaidi100.endpoints"); endpoints != nil {
			config.APIEndpoints = endpoints
		}

		if methods := ca.getMapStringString("providers.kuaidi100.methods"); methods != nil {
			config.APIMethods = methods
		}

		if statusCodes := ca.getMapStringString("providers.kuaidi100.status_codes"); statusCodes != nil {
			config.StatusCodes = statusCodes
		}

		return config, true

	case "yida":
		if !ca.manager.GetBool("providers.yida.enabled") {
			return nil, false
		}

		config := &ProviderConfig{
			Name: "yida",
		}
		return config, true

	case "yuntong":
		if !ca.manager.GetBool("providers.yuntong.enabled") {
			return nil, false
		}

		config := &ProviderConfig{
			Name: "yuntong",
		}
		return config, true

	default:
		return nil, false
	}
}

// getMapStringString 获取map[string]string类型的配置
func (ca *ConfigAdapter) getMapStringString(key string) map[string]string {
	// 从viper获取嵌套map配置
	rawMap := ca.manager.viper.GetStringMapString(key)
	if rawMap == nil {
		return nil
	}

	result := make(map[string]string)
	for k, v := range rawMap {
		result[k] = v
	}
	return result
}

// GetExpressConfigLegacy 获取传统格式的快递公司配置
func (ca *ConfigAdapter) GetExpressConfigLegacy() *ExpressCompaniesConfig {
	// 返回默认的快递公司配置
	// 实际实现中应该从统一配置中读取
	return &ExpressCompaniesConfig{
		EnabledCompanies: []ExpressCompany{
			{Code: "ZTO", Name: "中通快递"},
			{Code: "STO", Name: "申通快递"},
			{Code: "YTO", Name: "圆通快递"},
			{Code: "YD", Name: "韵达快递"},
			{Code: "JD", Name: "京东快递"},
			{Code: "JT", Name: "极兔快递"},
			{Code: "DBL", Name: "德邦快递"},
		},
	}
}

// IsProviderEnabled 检查供应商是否启用
func (ca *ConfigAdapter) IsProviderEnabled(provider string) bool {
	ca.mu.RLock()
	defer ca.mu.RUnlock()

	return ca.manager.GetBool(fmt.Sprintf("providers.%s.enabled", provider))
}

// GetProviderTimeout 获取供应商超时配置
func (ca *ConfigAdapter) GetProviderTimeout(provider string) time.Duration {
	ca.mu.RLock()
	defer ca.mu.RUnlock()

	return ca.manager.GetDuration(fmt.Sprintf("providers.%s.timeout", provider))
}

// GetProviderAPIKey 获取供应商API密钥
func (ca *ConfigAdapter) GetProviderAPIKey(provider string) string {
	ca.mu.RLock()
	defer ca.mu.RUnlock()

	switch provider {
	case "kuaidi100":
		return ca.manager.GetString("providers.kuaidi100.api_key")
	case "yida":
		return ca.manager.GetString("providers.yida.private_key")
	case "yuntong":
		return ca.manager.GetString("providers.yuntong.api_key")
	default:
		return ""
	}
}

// GetCallbackURL 获取回调URL
func (ca *ConfigAdapter) GetCallbackURL(provider string) string {
	ca.mu.RLock()
	defer ca.mu.RUnlock()

	baseURL := ca.manager.GetString("callback.base_url")
	if baseURL == "" {
		baseURL = os.Getenv("CALLBACK_BASE_URL")
		if baseURL == "" {
			baseURL = ca.manager.GetString("callback.default_base_url")
			if baseURL == "" {
				baseURL = "https://zsljygamznex.sealosgzg.site"
			}
		}
	}

	return fmt.Sprintf("%s/api/v1/callback/%s", baseURL, provider)
}

// GetFileUploadConfig 获取文件上传配置
func (ca *ConfigAdapter) GetFileUploadConfig() map[string]interface{} {
	ca.mu.RLock()
	defer ca.mu.RUnlock()

	return map[string]interface{}{
		"type":       ca.manager.GetString("workorder.file_upload.type"),
		"upload_dir": ca.manager.GetString("workorder.file_upload.upload_dir"),
		"base_url":   ca.manager.GetString("workorder.file_upload.base_url"),
		"oss": map[string]interface{}{
			"endpoint":          ca.manager.GetString("workorder.file_upload.oss.endpoint"),
			"access_key_id":     ca.manager.GetString("workorder.file_upload.oss.access_key_id"),
			"access_key_secret": ca.manager.GetString("workorder.file_upload.oss.access_key_secret"),
			"bucket_name":       ca.manager.GetString("workorder.file_upload.oss.bucket_name"),
		},
	}
}

// GetFeatureFlag 获取功能开关状态
func (ca *ConfigAdapter) GetFeatureFlag(feature string) bool {
	ca.mu.RLock()
	defer ca.mu.RUnlock()

	return ca.manager.GetBool(fmt.Sprintf("features.%s", feature))
}

// GetEnvironment 获取环境配置
func (ca *ConfigAdapter) GetEnvironment() string {
	ca.mu.RLock()
	defer ca.mu.RUnlock()

	env := ca.manager.GetString("environment")
	if env == "" {
		env = "development"
	}
	return env
}

// IsDevelopment 是否为开发环境
func (ca *ConfigAdapter) IsDevelopment() bool {
	return ca.GetEnvironment() == "development"
}

// IsProduction 是否为生产环境
func (ca *ConfigAdapter) IsProduction() bool {
	return ca.GetEnvironment() == "production"
}

// Reload 重新加载配置
func (ca *ConfigAdapter) Reload() error {
	ca.mu.Lock()
	defer ca.mu.Unlock()

	return ca.manager.Load()
}
