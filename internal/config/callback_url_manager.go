package config

import (
	"fmt"
	"os"
	"strings"
	"sync"

	"go.uber.org/zap"
)

// CallbackURLConfig 回调URL配置结构
type CallbackURLConfig struct {
	BaseURL   string                 `json:"base_url" mapstructure:"base_url"`
	Endpoints map[string]string      `json:"endpoints" mapstructure:"endpoints"`
	Fallback  CallbackFallbackConfig `json:"fallback" mapstructure:"fallback"`
}

// CallbackFallbackConfig 回调URL降级配置
type CallbackFallbackConfig struct {
	Enabled bool   `json:"enabled" mapstructure:"enabled"`
	BaseURL string `json:"base_url" mapstructure:"base_url"`
}

// CallbackURLManager 回调URL管理器
// 遵循单一职责原则：专门负责回调URL的配置管理
type CallbackURLManager struct {
	configManager *ConfigManager
	logger        *zap.Logger
	mu            sync.RWMutex
	cache         map[string]string // 缓存已构建的URL
}

var (
	callbackURLManager     *CallbackURLManager
	callbackURLManagerOnce sync.Once
)

// GetCallbackURLManager 获取回调URL管理器单例
// 遵循依赖倒置原则：依赖抽象而非具体实现
func GetCallbackURLManager() *CallbackURLManager {
	callbackURLManagerOnce.Do(func() {
		logger, _ := zap.NewProduction()
		callbackURLManager = &CallbackURLManager{
			configManager: GetConfigManager(),
			logger:        logger,
			cache:         make(map[string]string),
		}
	})
	return callbackURLManager
}

// GetCallbackURL 获取指定供应商的回调URL
// 遵循开闭原则：对扩展开放，对修改关闭
func (m *CallbackURLManager) GetCallbackURL(provider string) string {
	m.mu.RLock()
	// 检查缓存
	if url, exists := m.cache[provider]; exists {
		m.mu.RUnlock()
		return url
	}
	m.mu.RUnlock()

	// 构建URL
	url := m.buildCallbackURL(provider)

	// 更新缓存
	m.mu.Lock()
	m.cache[provider] = url
	m.mu.Unlock()

	return url
}

// buildCallbackURL 构建回调URL
// 遵循DRY原则：避免重复代码
func (m *CallbackURLManager) buildCallbackURL(provider string) string {
	// 1. 优先使用环境变量（最高优先级）
	if envURL := m.getFromEnvironment(provider); envURL != "" {
		m.logger.Info("Using callback URL from environment",
			zap.String("provider", provider),
			zap.String("url", envURL))
		return envURL
	}

	// 2. 使用配置文件中的设置
	if configURL := m.getFromConfig(provider); configURL != "" {
		m.logger.Info("Using callback URL from config",
			zap.String("provider", provider),
			zap.String("url", configURL))
		return configURL
	}

	// 3. 使用降级配置
	if fallbackURL := m.getFallbackURL(provider); fallbackURL != "" {
		m.logger.Warn("Using fallback callback URL",
			zap.String("provider", provider),
			zap.String("url", fallbackURL))
		return fallbackURL
	}

	// 4. 最后的默认值 - 从配置文件获取或使用系统默认
	baseURL := os.Getenv("CALLBACK_BASE_URL")
	if baseURL == "" {
		baseURL = m.configManager.GetString("callback.default_base_url")
		if baseURL == "" {
			baseURL = "https://zsljygamznex.sealosgzg.site" // 系统默认值
		}
	}

	endpoint := m.getEndpoint(provider)
	defaultURL := baseURL + endpoint

	m.logger.Warn("Using default callback URL",
		zap.String("provider", provider),
		zap.String("url", defaultURL),
		zap.String("base_url", baseURL))
	return defaultURL
}

// getFromEnvironment 从环境变量获取回调URL
func (m *CallbackURLManager) getFromEnvironment(provider string) string {
	// 检查特定供应商的环境变量
	specificKey := fmt.Sprintf("CALLBACK_URL_%s", strings.ToUpper(provider))
	if url := os.Getenv(specificKey); url != "" {
		return url
	}

	// 检查通用基础URL环境变量
	if baseURL := os.Getenv("CALLBACK_BASE_URL"); baseURL != "" {
		endpoint := m.getEndpoint(provider)
		return baseURL + endpoint
	}

	return ""
}

// getFromConfig 从配置文件获取回调URL
func (m *CallbackURLManager) getFromConfig(provider string) string {
	// 获取回调配置
	baseURL := m.configManager.GetString("callback.base_url")
	if baseURL == "" {
		return ""
	}

	endpoint := m.configManager.GetString(fmt.Sprintf("callback.endpoints.%s", provider))
	if endpoint == "" {
		endpoint = m.getEndpoint(provider)
	}

	return baseURL + endpoint
}

// getFallbackURL 获取降级回调URL
func (m *CallbackURLManager) getFallbackURL(provider string) string {
	// 检查是否启用降级
	if !m.configManager.GetBool("callback.fallback.enabled") {
		return ""
	}

	baseURL := m.configManager.GetString("callback.fallback.base_url")
	if baseURL == "" {
		return ""
	}

	endpoint := m.getEndpoint(provider)
	return baseURL + endpoint
}

// getEndpoint 获取供应商的端点路径
func (m *CallbackURLManager) getEndpoint(provider string) string {
	// 标准化端点路径
	endpoints := map[string]string{
		"kuaidi100":  "/webhook/kuaidi100",
		"yida":       "/webhook/yida",
		"yuntong":    "/webhook/yuntong",
		"kuaidiniao": "/webhook/kuaidiniao",
		"cainiao":    "/webhook/cainiao",
	}

	if endpoint, exists := endpoints[provider]; exists {
		return endpoint
	}

	// 默认端点格式
	return fmt.Sprintf("/webhook/%s", provider)
}

// RefreshCache 刷新缓存
// 遵循接口隔离原则：提供必要的接口
func (m *CallbackURLManager) RefreshCache() {
	m.mu.Lock()
	defer m.mu.Unlock()

	// 清空缓存，强制重新构建
	m.cache = make(map[string]string)
	m.logger.Info("Callback URL cache refreshed")
}

// GetAllCallbackURLs 获取所有供应商的回调URL
func (m *CallbackURLManager) GetAllCallbackURLs() map[string]string {
	providers := []string{"kuaidi100", "yida", "yuntong"}
	urls := make(map[string]string)

	for _, provider := range providers {
		urls[provider] = m.GetCallbackURL(provider)
	}

	return urls
}

// ValidateConfig 验证回调URL配置
func (m *CallbackURLManager) ValidateConfig() error {
	// 检查基础URL是否配置
	baseURL := m.configManager.GetString("callback.base_url")
	envBaseURL := os.Getenv("CALLBACK_BASE_URL")

	if baseURL == "" && envBaseURL == "" {
		return fmt.Errorf("callback base URL not configured in config file or environment variables")
	}

	m.logger.Info("Callback URL configuration validated successfully")
	return nil
}
