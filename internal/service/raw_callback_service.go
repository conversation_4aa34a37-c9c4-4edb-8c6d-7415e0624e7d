package service

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/your-org/go-kuaidi/internal/model"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// RawCallbackService 原始回调数据服务
type RawCallbackService struct {
	callbackDB *gorm.DB // 回调接收服务数据库
	mainDB     *gorm.DB // 主数据库（用于查询 unified_callback_records）
	logger     *zap.Logger
}

// NewRawCallbackService 创建原始回调数据服务
func NewRawCallbackService(callbackDB *gorm.DB, logger *zap.Logger) *RawCallbackService {
	return &RawCallbackService{
		callbackDB: callbackDB,
		mainDB:     callbackDB, // 默认使用相同的数据库连接
		logger:     logger,
	}
}

// NewRawCallbackServiceWithMainDB 创建原始回调数据服务（带主数据库连接）
func NewRawCallbackServiceWithMainDB(callbackDB *gorm.DB, mainDB *gorm.DB, logger *zap.Logger) *RawCallbackService {
	return &RawCallbackService{
		callbackDB: callbackDB,
		mainDB:     mainDB,
		logger:     logger,
	}
}

// GetRawCallbackList 获取原始回调列表
func (s *RawCallbackService) GetRawCallbackList(page, pageSize int, conditions map[string]interface{}) ([]model.RawCallbackData, int64, error) {
	var records []model.RawCallbackData
	var total int64

	// 🔍 调试：检查数据库连接和表
	s.logger.Info("🔍 开始查询原始回调数据",
		zap.Int("page", page),
		zap.Int("page_size", pageSize),
		zap.Any("conditions", conditions))

	// 构建查询
	query := s.callbackDB.Model(&model.RawCallbackData{})

	// 应用筛选条件
	if provider, ok := conditions["provider"].(string); ok && provider != "" {
		query = query.Where("provider = ?", provider)
	}

	if orderNoLike, ok := conditions["order_no_like"].(string); ok && orderNoLike != "" {
		query = query.Where("raw_body LIKE ?", "%"+orderNoLike+"%")
	}

	if trackingNoLike, ok := conditions["tracking_no_like"].(string); ok && trackingNoLike != "" {
		query = query.Where("raw_body LIKE ?", "%"+trackingNoLike+"%")
	}

	// 🔥 新增：工单ID搜索条件
	if workOrderIDLike, ok := conditions["work_order_id_like"].(string); ok && workOrderIDLike != "" {
		query = query.Where("raw_body LIKE ?", "%"+workOrderIDLike+"%")
	}

	if startTime, ok := conditions["start_time"].(time.Time); ok {
		query = query.Where("received_at >= ?", startTime)
	}

	if endTime, ok := conditions["end_time"].(time.Time); ok {
		query = query.Where("received_at <= ?", endTime)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		s.logger.Error("获取原始回调总数失败", zap.Error(err))
		return nil, 0, err
	}

	s.logger.Info("🔍 查询结果", zap.Int64("total", total))

	// 分页查询
	offset := (page - 1) * pageSize
	if err := query.Order("received_at DESC").Offset(offset).Limit(pageSize).Find(&records).Error; err != nil {
		s.logger.Error("获取原始回调列表失败", zap.Error(err))
		return nil, 0, err
	}

	return records, total, nil
}

// GetRawCallbackByID 根据ID获取原始回调记录
func (s *RawCallbackService) GetRawCallbackByID(id string) (*model.RawCallbackData, error) {
	var record model.RawCallbackData
	if err := s.callbackDB.Where("id = ?", id).First(&record).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		s.logger.Error("获取原始回调记录失败", zap.String("id", id), zap.Error(err))
		return nil, err
	}
	return &record, nil
}

// GetRawCallbackStatistics 获取原始回调统计
func (s *RawCallbackService) GetRawCallbackStatistics(conditions map[string]interface{}) (*model.RawCallbackStatistics, error) {
	stats := &model.RawCallbackStatistics{
		ProviderStats:  make(map[string]model.ProviderStatistics),
		EventTypeStats: make(map[string]model.EventTypeStatistics),
		DailyStats:     make([]model.DailyStatistics, 0),
	}

	// 构建基础查询
	baseQuery := s.callbackDB.Model(&model.RawCallbackData{})

	// 应用筛选条件
	if provider, ok := conditions["provider"].(string); ok && provider != "" {
		baseQuery = baseQuery.Where("provider = ?", provider)
	}

	if startTime, ok := conditions["start_time"].(time.Time); ok {
		baseQuery = baseQuery.Where("received_at >= ?", startTime)
	}

	if endTime, ok := conditions["end_time"].(time.Time); ok {
		baseQuery = baseQuery.Where("received_at <= ?", endTime)
	}

	// 获取总记录数
	if err := baseQuery.Count(&stats.TotalRecords).Error; err != nil {
		s.logger.Error("获取总记录数失败", zap.Error(err))
		return nil, err
	}

	// 获取供应商统计
	if err := s.getProviderStatistics(baseQuery, stats); err != nil {
		s.logger.Error("获取供应商统计失败", zap.Error(err))
		return nil, err
	}

	// 获取每日统计
	if err := s.getDailyStatistics(baseQuery, stats); err != nil {
		s.logger.Error("获取每日统计失败", zap.Error(err))
		return nil, err
	}

	// 计算成功率
	if stats.TotalRecords > 0 {
		stats.SuccessRate = float64(stats.SuccessRecords) / float64(stats.TotalRecords) * 100
	}

	return stats, nil
}

// getProviderStatistics 获取供应商统计
func (s *RawCallbackService) getProviderStatistics(baseQuery *gorm.DB, stats *model.RawCallbackStatistics) error {
	type ProviderStat struct {
		Provider string `json:"provider"`
		Total    int64  `json:"total"`
	}

	var providerStats []ProviderStat
	if err := baseQuery.Select("provider, COUNT(*) as total").Group("provider").Find(&providerStats).Error; err != nil {
		return err
	}

	for _, stat := range providerStats {
		stats.ProviderStats[stat.Provider] = model.ProviderStatistics{
			Total:   stat.Total,
			Success: stat.Total, // 简化处理，假设都是成功的
			Failed:  0,
			Pending: 0,
		}
		stats.SuccessRecords += stat.Total
	}

	return nil
}

// getDailyStatistics 获取每日统计
func (s *RawCallbackService) getDailyStatistics(baseQuery *gorm.DB, stats *model.RawCallbackStatistics) error {
	type DailyStat struct {
		Date  string `json:"date"`
		Total int64  `json:"total"`
	}

	var dailyStats []DailyStat

	// 根据数据库类型选择日期格式化函数
	dateFormat := "DATE(received_at)"
	if s.isPostgreSQL() {
		dateFormat = "DATE(received_at)"
	}

	if err := baseQuery.Select(fmt.Sprintf("%s as date, COUNT(*) as total", dateFormat)).
		Group("DATE(received_at)").
		Order("date DESC").
		Limit(30). // 最近30天
		Find(&dailyStats).Error; err != nil {
		return err
	}

	for _, stat := range dailyStats {
		stats.DailyStats = append(stats.DailyStats, model.DailyStatistics{
			Date:    stat.Date,
			Total:   stat.Total,
			Success: stat.Total, // 简化处理
			Failed:  0,
			Pending: 0,
		})
	}

	return nil
}

// isPostgreSQL 检查是否是PostgreSQL数据库
func (s *RawCallbackService) isPostgreSQL() bool {
	return strings.Contains(strings.ToLower(s.callbackDB.Dialector.Name()), "postgres")
}

// UnifiedCallbackService 统一回调服务接口（用于重推）
type UnifiedCallbackService struct {
	orderCallbackService interface {
		ProcessCallback(ctx context.Context, provider string, rawData []byte, headers map[string]string) (*model.CallbackResponse, error)
	}
	workOrderCallbackService interface {
		ProcessCallback(ctx context.Context, provider string, rawData []byte, headers map[string]string) (*model.CallbackResponse, error)
	}
	logger *zap.Logger
}

// NewUnifiedCallbackService 创建统一回调服务
func NewUnifiedCallbackService(
	orderCallbackService interface {
		ProcessCallback(ctx context.Context, provider string, rawData []byte, headers map[string]string) (*model.CallbackResponse, error)
	},
	workOrderCallbackService interface {
		ProcessCallback(ctx context.Context, provider string, rawData []byte, headers map[string]string) (*model.CallbackResponse, error)
	},
	logger *zap.Logger,
) *UnifiedCallbackService {
	return &UnifiedCallbackService{
		orderCallbackService:     orderCallbackService,
		workOrderCallbackService: workOrderCallbackService,
		logger:                   logger,
	}
}

// ReprocessRawCallback 重新处理原始回调
func (s *UnifiedCallbackService) ReprocessRawCallback(provider string, rawBody []byte) error {
	s.logger.Info("开始重新处理原始回调",
		zap.String("provider", provider),
		zap.Int("body_length", len(rawBody)))

	ctx := context.Background()
	headers := make(map[string]string) // 重推时没有原始请求头，使用空的headers

	// 根据供应商判断走订单还是工单回调处理器
	providerLower := strings.ToLower(provider)
	isWorkOrderProvider := providerLower == "kuaidi100" || providerLower == "yida" || providerLower == "yuntong" || providerLower == "kuaidiniao" || providerLower == "kuaidi_niao" || providerLower == "kuaidi-niao"

	var (
		response *model.CallbackResponse
		err      error
	)

	if isWorkOrderProvider {
		// 走工单回调处理器
		response, err = s.workOrderCallbackService.ProcessCallback(ctx, providerLower, rawBody, headers)
	} else {
		// 走订单回调处理器
		response, err = s.orderCallbackService.ProcessCallback(ctx, providerLower, rawBody, headers)
	}
	if err != nil {
		s.logger.Error("重推回调处理失败",
			zap.String("provider", provider),
			zap.Error(err))
		return fmt.Errorf("重推回调处理失败: %w", err)
	}

	s.logger.Info("重推回调处理成功",
		zap.String("provider", provider),
		zap.Bool("success", response.Success),
		zap.String("message", response.Message))

	return nil
}
