package callback

import (
	"database/sql"

	"go.uber.org/zap"

	"github.com/your-org/go-kuaidi/internal/config"
	"github.com/your-org/go-kuaidi/internal/constants"
	"github.com/your-org/go-kuaidi/internal/express"
	"github.com/your-org/go-kuaidi/internal/repository"
	"github.com/your-org/go-kuaidi/internal/service"
)

// ServiceManager 回调服务管理器
type ServiceManager struct {
	config             *config.CallbackConfig
	db                 *sql.DB
	logger             *zap.Logger
	unifiedService     *UnifiedCallbackService
	callbackRepository repository.CallbackRepository
	orderRepository    repository.OrderRepository
	billingService     service.BillingService
}

// NewServiceManager 创建回调服务管理器
func NewServiceManager(
	cfg *config.CallbackConfig,
	db *sql.DB,
	orderRepo repository.OrderRepository,
	mappingService express.ExpressMappingService,
	systemConfigService service.SystemConfigService, // 🔥 新增：系统配置服务
	logger *zap.Logger,
) *ServiceManager {
	// 创建回调数据仓库
	callbackRepo := repository.NewPostgresCallbackRepository(db)

	// 创建计费服务
	billingRepo := repository.NewPostgresBillingRepository(db)
	balanceRepo := repository.NewPostgresBalanceRepository(db)
	balanceService := service.NewBalanceService(balanceRepo, orderRepo, db, logger)
	billingService := service.NewBillingService(orderRepo, billingRepo, balanceService, logger)

	// 创建快递公司仓储
	expressCompanyRepo := express.NewPostgresExpressCompanyRepository(db, logger)

	// 🔥 新增：创建智能订单查找服务
	smartOrderFinder := service.NewSmartOrderFinder(orderRepo, logger)

	// 🔥 修复：暂时跳过OrderStatusUpdater的创建，因为需要GORM数据库连接
	// 这个问题需要在系统架构层面解决，暂时使用nil作为临时解决方案
	logger.Warn("⚠️ OrderStatusUpdater未创建，订单取消可能无法正确退款")

	// 创建统一回调服务
	unifiedService := NewUnifiedCallbackService(callbackRepo, orderRepo, billingService, balanceService, mappingService, expressCompanyRepo, smartOrderFinder, systemConfigService, nil, logger)

	return &ServiceManager{
		config:             cfg,
		db:                 db,
		logger:             logger,
		unifiedService:     unifiedService,
		callbackRepository: callbackRepo,
		orderRepository:    orderRepo,
		billingService:     billingService,
	}
}

// Initialize 初始化回调系统
func (sm *ServiceManager) Initialize() error {
	sm.logger.Info("初始化回调系统")

	// 验证配置
	if err := sm.config.Validate(); err != nil {
		return err
	}

	// 注册供应商适配器
	sm.registerProviderAdapters()

	sm.logger.Info("回调系统初始化完成")
	return nil
}

// registerProviderAdapters 注册供应商适配器
func (sm *ServiceManager) registerProviderAdapters() {
	// 注册云通适配器
	if sm.config.IsProviderEnabled(constants.ProviderYuntong) {
		yuntongAdapter := NewYuntongCallbackAdapter(
			sm.config.Providers.Yuntong.EBusinessID,
			sm.config.Providers.Yuntong.SecretKey,
		)
		sm.unifiedService.RegisterProviderAdapter(constants.ProviderYuntong, yuntongAdapter)
		sm.logger.Info("已注册云通回调适配器")
	}

	// 注册易达适配器
	if sm.config.IsProviderEnabled(constants.ProviderYida) {
		yidaAdapter := NewYidaCallbackAdapter()
		sm.unifiedService.RegisterProviderAdapter(constants.ProviderYida, yidaAdapter)
		sm.logger.Info("已注册易达回调适配器")
	}

	// 注册快递100适配器
	if sm.config.IsProviderEnabled(constants.ProviderKuaidi100) {
		kuaidi100Adapter := NewKuaidi100CallbackAdapter(
			sm.config.Providers.Kuaidi100.Salt,
		)
		sm.unifiedService.RegisterProviderAdapter(constants.ProviderKuaidi100, kuaidi100Adapter)
		sm.logger.Info("已注册快递100回调适配器")
	}

	// 注册菜鸟裹裹适配器
	if sm.config.IsProviderEnabled(constants.ProviderCainiao) {
		cainiaoAdapter := NewCainiaoCallbackAdapter(
			sm.config.Providers.Cainiao.AccessCode,
		)
		sm.unifiedService.RegisterProviderAdapter(constants.ProviderCainiao, cainiaoAdapter)
		sm.logger.Info("已注册菜鸟裹裹回调适配器")
	}
}

// GetUnifiedService 获取统一回调服务
func (sm *ServiceManager) GetUnifiedService() *UnifiedCallbackService {
	return sm.unifiedService
}

// GetCallbackRepository 获取回调数据仓库
func (sm *ServiceManager) GetCallbackRepository() repository.CallbackRepository {
	return sm.callbackRepository
}

// GetConfig 获取配置
func (sm *ServiceManager) GetConfig() *config.CallbackConfig {
	return sm.config
}

// Shutdown 关闭回调系统
func (sm *ServiceManager) Shutdown() error {
	sm.logger.Info("关闭回调系统")
	// 这里可以添加清理逻辑
	return nil
}
