package callback

import (
	"context"
	"encoding/json"
	"fmt"
	"math"
	"strconv"
	"strings"
	"time"

	"github.com/your-org/go-kuaidi/internal/constants"
	"github.com/your-org/go-kuaidi/internal/express"
	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/util"
	"go.uber.org/zap"
)

// CallbackStandardizer 回调数据标准化器
type CallbackStandardizer struct {
	statusMapper   *UnifiedStatusMapper
	mappingService express.ExpressMappingService
	logger         *zap.Logger
}

// NewCallbackStandardizer 创建回调数据标准化器
func NewCallbackStandardizer(mappingService express.ExpressMappingService, logger *zap.Logger) *CallbackStandardizer {
	return &CallbackStandardizer{
		statusMapper:   NewUnifiedStatusMapper(),
		mappingService: mappingService,
		logger:         logger,
	}
}

// Standardize 标准化回调数据
func (s *CallbackStandardizer) Standardize(provider string, parsedData *model.ParsedCallbackData) (*model.StandardizedCallbackData, error) {
	switch provider {
	case model.CallbackProviderYuntong:
		return s.standardizeYuntong(parsedData)
	case model.CallbackProviderYida:
		return s.standardizeYida(parsedData)
	case model.CallbackProviderKuaidi100:
		return s.standardizeKuaidi100(parsedData)
	case model.CallbackProviderCainiao:
		return s.standardizeCainiao(parsedData)
	case model.CallbackProviderKuaidiniao:
		return s.standardizeKuaidiniao(parsedData)
	default:
		return nil, fmt.Errorf("不支持的供应商: %s", provider)
	}
}

// standardizeYuntong 标准化云通回调数据
func (s *CallbackStandardizer) standardizeYuntong(parsedData *model.ParsedCallbackData) (*model.StandardizedCallbackData, error) {
	switch parsedData.Type {
	case "order_status_changed":
		return s.standardizeYuntongOrderPush(parsedData)
	case "billing_updated":
		return s.standardizeYuntongBillingPush(parsedData)
	case "ticket_replied":
		return s.standardizeYuntongTicketReply(parsedData)
	default:
		return nil, fmt.Errorf("未知的云通回调类型: %s", parsedData.Type)
	}
}

// standardizeYuntongOrderPush 标准化云通订单推送
func (s *CallbackStandardizer) standardizeYuntongOrderPush(parsedData *model.ParsedCallbackData) (*model.StandardizedCallbackData, error) {
	// 🔥 企业级修复：支持多种云通回调数据格式
	type YuntongOrderData struct {
		ShipperCode      string  `json:"ShipperCode"`
		OrderCode        string  `json:"OrderCode"`
		LogisticCode     string  `json:"LogisticCode"`
		Reason           string  `json:"Reason"`
		State            int     `json:"State"`
		Comments         string  `json:"comments"`
		CreateTime       string  `json:"CreateTime"`
		OperateType      int     `json:"OperateType"`
		Weight           float64 `json:"Weight"`
		Cost             float64 `json:"Cost"`
		InsureAmount     float64 `json:"InsureAmount"`
		PackageFee       float64 `json:"PackageFee"`
		OtherFee         float64 `json:"OtherFee"`
		TotalFee         float64 `json:"TotalFee"`
		Volume           float64 `json:"Volume"`
		OfficialFee      float64 `json:"OfficialFee"`
		ChargedWeight    float64 `json:"chargedWeight"`
		BackLogisticCode string  `json:"BackLogisticCode"`
		BackFee          float64 `json:"BackFee"`
		Quantity         int     `json:"Quantity"`
		ExpType          string  `json:"ExpType"`
		PayType          int     `json:"PayType"`
		PickerInfo       []struct {
			PersonName     string `json:"PersonName"`
			PersonTel      string `json:"PersonTel"`
			PersonCode     string `json:"PersonCode"`
			StationName    string `json:"StationName"`
			StationCode    string `json:"StationCode"`
			StationAddress string `json:"StationAddress"`
			StationTel     string `json:"StationTel"`
			PickupCode     string `json:"PickupCode"`
		} `json:"PickerInfo"`
		Costing struct {
			Discount    int     `json:"discount"`
			UpperGround int     `json:"upperGround"`
			GroundPrice float64 `json:"groundPrice"`
			RateOfStage float64 `json:"rateOfStage"`
		} `json:"Costing"`
	}

	// 使用JSON转换来处理结构体
	jsonData, err := json.Marshal(parsedData.Data)
	if err != nil {
		return nil, fmt.Errorf("云通订单数据序列化失败: %w", err)
	}

	var orderData YuntongOrderData
	if err := json.Unmarshal(jsonData, &orderData); err != nil {
		return nil, fmt.Errorf("云通订单数据反序列化失败: %w", err)
	}

	// 🔥 企业级修复：将云通的快递公司代码转换为系统内部标准代码
	// 这样体积重量计算器就能找到正确的快递公司配置
	if orderData.ShipperCode != "" {
		// 🔥 修复：使用数据库映射服务替代配置文件
		if s.mappingService != nil {
			ctx := context.Background()
			if code, err := s.mappingService.GetStandardCompanyCode(ctx, orderData.ShipperCode, "yuntong"); err == nil {
				fmt.Printf("🔧 云通快递公司代码映射成功 - 云通代码: %s, 标准代码: %s\n",
					orderData.ShipperCode, code)
			} else {
				fmt.Printf("⚠️ 云通快递公司代码映射失败，使用原代码: %s\n",
					orderData.ShipperCode)
			}
		}
	}

	// 处理快递员信息
	// 🔥 企业级修复：对于取消状态，不处理揽件员信息，避免清空已有数据
	var courierInfo *model.CourierInfo
	if len(orderData.PickerInfo) > 0 {
		picker := orderData.PickerInfo[0]
		courierInfo = &model.CourierInfo{
			Name:       picker.PersonName,
			Phone:      picker.PersonTel,
			Code:       picker.PersonCode,
			Station:    picker.StationName,
			PickupCode: picker.PickupCode, // 🔥 修复：提取取件码
		}
	} else if orderData.State == 203 { // 取消状态
		// 对于取消状态，如果没有PickerInfo，不设置courierInfo
		// 这样可以避免传递nil值，保留现有的揽件员信息
		courierInfo = nil
	}

	// 解析时间
	updateTime, err := time.Parse("2006-01-02 15:04:05", orderData.CreateTime)
	if err != nil {
		updateTime = util.NowBeijing()
	}

	// 根据状态码判断事件类型
	var eventType string
	var eventData interface{}

	if orderData.State == 301 || orderData.State == 208 || orderData.State == 501 {
		// 计费相关状态
		eventType = model.EventTypeBillingUpdated

		// 构建费用明细，包含退回费用
		feeDetails := []model.FeeDetail{}
		if orderData.Cost > 0 {
			feeDetails = append(feeDetails, model.FeeDetail{Type: "freight", Name: "运费", Amount: orderData.Cost})
		}
		if orderData.InsureAmount > 0 {
			feeDetails = append(feeDetails, model.FeeDetail{Type: "insurance", Name: "保价费", Amount: orderData.InsureAmount})
		}
		if orderData.PackageFee > 0 {
			feeDetails = append(feeDetails, model.FeeDetail{Type: "package", Name: "包装费", Amount: orderData.PackageFee})
		}
		if orderData.OtherFee > 0 {
			feeDetails = append(feeDetails, model.FeeDetail{Type: "other", Name: "其他费用", Amount: orderData.OtherFee})
		}
		// 🔥 新增：处理退回费用
		if orderData.BackFee > 0 {
			feeDetails = append(feeDetails, model.FeeDetail{Type: "return_fee", Name: "退回费用", Amount: orderData.BackFee})
		}

		// 计算总费用（包含退回费用）
		totalFee := orderData.Cost + orderData.InsureAmount + orderData.PackageFee + orderData.OtherFee + orderData.BackFee

		eventData = &model.BillingUpdatedData{
			Weight:        orderData.Weight,
			Volume:        orderData.Volume,
			Cost:          orderData.Cost,
			TotalFee:      totalFee, // 使用计算后的总费用
			ChargedWeight: orderData.ChargedWeight,
			UpdateTime:    updateTime,
			FeeDetails:    feeDetails,
		}
	} else {
		// 状态变更
		eventType = model.EventTypeOrderStatusChanged

		// 构建数据映射
		dataMap := map[string]interface{}{
			"Weight":           orderData.Weight,
			"Cost":             orderData.Cost,
			"InsureAmount":     orderData.InsureAmount,
			"PackageFee":       orderData.PackageFee,
			"OtherFee":         orderData.OtherFee,
			"chargedWeight":    orderData.ChargedWeight,
			"BackLogisticCode": orderData.BackLogisticCode,
			"BackFee":          orderData.BackFee,
		}

		statusResult := s.statusMapper.MapYuntongStatus(orderData.State, dataMap)

		// 🔥 企业级修复：对于取消状态，不传递CourierInfo，避免影响已有的揽件员信息
		var finalCourierInfo *model.CourierInfo
		if orderData.State == 203 { // 取消状态
			// 取消状态不传递CourierInfo，保留数据库中已有的揽件员信息
			finalCourierInfo = nil
			fmt.Printf("🔧 云通取消状态修复 - 订单: %s, 状态: %d, 不传递CourierInfo以保留已有揽件员信息\n",
				orderData.OrderCode, orderData.State)
		} else {
			// 其他状态正常传递CourierInfo
			finalCourierInfo = courierInfo
			if courierInfo != nil {
				fmt.Printf("🔧 云通状态更新 - 订单: %s, 状态: %d, 传递CourierInfo: %s %s\n",
					orderData.OrderCode, orderData.State, courierInfo.Name, courierInfo.Phone)
			}
		}

		// 🔥 修复：提取取件码到Extra字段（作为备用）
		extraData := map[string]interface{}{
			"provider_status": orderData.State,
			"provider_desc":   orderData.Reason,
			"comments":        orderData.Comments,
			"operate_type":    orderData.OperateType,
			"raw_status":      statusResult.RawStatus,
			"status_extra":    statusResult.Extra,
		}

		// 🔥 修复：如果有取件码，添加到Extra字段
		if len(orderData.PickerInfo) > 0 && orderData.PickerInfo[0].PickupCode != "" {
			extraData["pickup_code"] = orderData.PickerInfo[0].PickupCode
		}

		eventData = &model.OrderStatusChangedData{
			NewStatus:   statusResult.Status,
			StatusDesc:  statusResult.GetDescription(),
			UpdateTime:  updateTime,
			CourierInfo: finalCourierInfo,
			Extra:       extraData,
		}
	}

	// 🔥 企业级兼容性修复：云通301计费状态同时触发运输中状态
	standardizedData := &model.StandardizedCallbackData{
		EventType:       eventType,
		OrderNo:         parsedData.OrderNo,
		CustomerOrderNo: parsedData.CustomerOrderNo,
		TrackingNo:      parsedData.TrackingNo,
		Provider:        model.CallbackProviderYuntong,
		Timestamp:       updateTime,
		Data:            eventData,
	}

	// 🔥 特殊处理：301计费状态需要额外生成运输中状态回调
	if orderData.State == 301 {
		s.logger.Info("云通301计费状态兼容性处理 - 同时生成运输中状态",
			zap.String("order_no", parsedData.CustomerOrderNo),
			zap.Int("state", orderData.State))

		// 设置额外标记，表示需要生成运输中状态
		if standardizedData.Data != nil {
			if statusData, ok := standardizedData.Data.(*model.OrderStatusChangedData); ok {
				if statusData.Extra == nil {
					statusData.Extra = make(map[string]interface{})
				}
				statusData.Extra["generate_in_transit"] = true
				statusData.Extra["in_transit_reason"] = "云通301计费状态兼容性处理"
			}
		}
	}

	return standardizedData, nil
}

// standardizeYuntongBillingPush 标准化云通计费推送
func (s *CallbackStandardizer) standardizeYuntongBillingPush(parsedData *model.ParsedCallbackData) (*model.StandardizedCallbackData, error) {
	// 云通计费推送数据结构
	type YuntongBillingData struct {
		ShipperCode      string  `json:"ShipperCode"`
		MonthCode        string  `json:"MonthCode"`
		OrderCode        string  `json:"OrderCode"`
		LogisticCode     string  `json:"LogisticCode"`
		Reason           string  `json:"Reason"`
		State            int     `json:"State"`
		Comments         string  `json:"comments"`
		CreateTime       string  `json:"CreateTime"`
		OperateType      int     `json:"OperateType"`
		Weight           float64 `json:"Weight"`
		Cost             float64 `json:"Cost"`
		InsureAmount     float64 `json:"InsureAmount"`
		PackageFee       float64 `json:"PackageFee"`
		OtherFee         float64 `json:"OtherFee"`
		TotalFee         float64 `json:"TotalFee"` // 🔥 修复：添加官方文档中的TotalFee字段
		Volume           float64 `json:"Volume"`
		OfficialFee      float64 `json:"OfficialFee"`
		ChargedWeight    float64 `json:"chargedWeight"`
		BackLogisticCode string  `json:"BackLogisticCode"`
		BackFee          float64 `json:"BackFee"`
		Quantity         int     `json:"Quantity"`
		ExpType          string  `json:"ExpType"`
		PayType          int     `json:"PayType"`
	}

	// 使用JSON转换来处理结构体
	jsonData, err := json.Marshal(parsedData.Data)
	if err != nil {
		return nil, fmt.Errorf("云通计费数据序列化失败: %w", err)
	}

	var billingData YuntongBillingData
	if err := json.Unmarshal(jsonData, &billingData); err != nil {
		return nil, fmt.Errorf("云通计费数据反序列化失败: %w", err)
	}

	// 🔥 企业级修复：将云通的快递公司代码转换为系统内部标准代码
	standardExpressCode := billingData.ShipperCode
	if billingData.ShipperCode != "" {
		// 🔥 修复：使用数据库映射服务替代配置文件
		if s.mappingService != nil {
			ctx := context.Background()
			if code, err := s.mappingService.GetStandardCompanyCode(ctx, billingData.ShipperCode, "yuntong"); err == nil {
				standardExpressCode = code
				fmt.Printf("🔧 云通计费快递公司代码映射成功 - 云通代码: %s, 标准代码: %s\n",
					billingData.ShipperCode, standardExpressCode)
			} else {
				fmt.Printf("⚠️ 云通计费快递公司代码映射失败，使用原代码: %s\n",
					billingData.ShipperCode)
			}
		}
	}

	// 解析时间
	updateTime, err := time.Parse("2006-01-02 15:04:05", billingData.CreateTime)
	if err != nil {
		updateTime = util.NowBeijing()
	}

	// 构建费用明细并识别超重费用
	feeDetails := []model.FeeDetail{}

	if billingData.Cost > 0 {
		feeDetails = append(feeDetails, model.FeeDetail{
			FeeType:   "freight",
			FeeDesc:   "运费",
			Amount:    billingData.Cost,
			PayStatus: "paid",
			// 兼容旧字段
			Type: "freight",
			Name: "运费",
		})
	}
	if billingData.InsureAmount > 0 {
		feeDetails = append(feeDetails, model.FeeDetail{
			FeeType:   "insurance",
			FeeDesc:   "保价费",
			Amount:    billingData.InsureAmount,
			PayStatus: "paid",
			// 兼容旧字段
			Type: "insurance",
			Name: "保价费",
		})
	}
	if billingData.PackageFee > 0 {
		feeDetails = append(feeDetails, model.FeeDetail{
			FeeType:   "package",
			FeeDesc:   "包装费",
			Amount:    billingData.PackageFee,
			PayStatus: "paid",
			// 兼容旧字段
			Type: "package",
			Name: "包装费",
		})
	}
	if billingData.OtherFee > 0 {
		// 云通的OtherFee可能包含超重费用，根据Comments字段判断
		feeType := "other"
		feeDesc := "其他费用"

		// 检查Comments字段是否包含超重相关信息
		if strings.Contains(billingData.Comments, "超重") || strings.Contains(billingData.Reason, "超重") {
			feeType = "overweight"
			feeDesc = "超重费用"
		} else if strings.Contains(billingData.Comments, "超轻") || strings.Contains(billingData.Reason, "超轻") {
			feeType = "underweight"
			feeDesc = "超轻费用"
		}

		feeDetails = append(feeDetails, model.FeeDetail{
			FeeType:   feeType,
			FeeDesc:   feeDesc,
			Amount:    billingData.OtherFee,
			PayStatus: "paid",
			// 兼容旧字段
			Type: "other",
			Name: feeDesc,
		})
	}

	// 🔥 新增：处理退回费用
	if billingData.BackFee > 0 {
		feeDetails = append(feeDetails, model.FeeDetail{
			FeeType:   "return_fee",
			FeeDesc:   "退回费用",
			Amount:    billingData.BackFee,
			PayStatus: "paid",
			// 兼容旧字段
			Type: "return_fee",
			Name: "退回费用",
		})
	}

	// 🔥 修复：优先使用云通提供的TotalFee字段，如果没有则手动计算
	var totalFee float64
	if billingData.TotalFee > 0 {
		// 使用云通提供的总费用，但需要加上退回费用（如果有）
		totalFee = billingData.TotalFee + billingData.BackFee
	} else {
		// 手动计算总费用（包含退回费用）
		totalFee = billingData.Cost + billingData.InsureAmount + billingData.PackageFee + billingData.OtherFee + billingData.BackFee
	}

	// 构建计费信息
	var pricingInfo *model.PricingInfo
	if billingData.Cost > 0 {
		// 从云通的Costing字段提取计费信息（如果有的话）
		pricingInfo = &model.PricingInfo{
			FirstWeight:   1.0, // 默认首重1kg
			FirstPrice:    billingData.Cost,
			ContinuePrice: 0,
			Discount:      100, // 默认无折扣
			DiscountFee:   0,
		}
	}

	// 🔥 修复：优先使用云通提供的计费重量，如果没有则计算
	var chargedWeight float64
	if billingData.ChargedWeight > 0 {
		// 云通已经提供了准确的计费重量，直接使用
		chargedWeight = billingData.ChargedWeight
	} else {
		// 云通没有提供计费重量，自己计算
		chargedWeight = s.calculateChargedWeightWithExpressCode(standardExpressCode, billingData.Weight, billingData.Volume)

		// 快递行业标准，计费重量最小为1kg
		if chargedWeight < 1.0 {
			chargedWeight = 1.0
		}
	}

	// 🔥 修复：云通返回的体积单位是cm³，需要转换为m³存储
	volumeInM3 := billingData.Volume / 1000000 // cm³ → m³

	eventData := &model.BillingUpdatedData{
		Weight:        billingData.Weight,
		Volume:        volumeInM3, // 🔥 修复：使用转换后的m³体积
		Cost:          billingData.Cost,
		ChargedWeight: chargedWeight,
		TotalFee:      totalFee,
		OfficialFee:   billingData.OfficialFee,
		PackageCount:  billingData.Quantity,
		FeeDetails:    feeDetails,
		PricingInfo:   pricingInfo,
		UpdateTime:    updateTime,
	}

	return &model.StandardizedCallbackData{
		EventType:       model.EventTypeBillingUpdated,
		OrderNo:         parsedData.OrderNo,
		CustomerOrderNo: parsedData.CustomerOrderNo,
		TrackingNo:      parsedData.TrackingNo,
		Provider:        model.CallbackProviderYuntong,
		Timestamp:       parsedData.Timestamp,
		Data:            eventData,
	}, nil
}

// standardizeYuntongTicketReply 标准化云通工单回复
func (s *CallbackStandardizer) standardizeYuntongTicketReply(parsedData *model.ParsedCallbackData) (*model.StandardizedCallbackData, error) {
	// 云通工单回复的处理逻辑
	return &model.StandardizedCallbackData{
		EventType:       model.EventTypeTicketReplied,
		OrderNo:         parsedData.OrderNo,
		CustomerOrderNo: parsedData.CustomerOrderNo,
		TrackingNo:      parsedData.TrackingNo,
		Provider:        model.CallbackProviderYuntong,
		Timestamp:       parsedData.Timestamp,
		Data:            parsedData.Data,
	}, nil
}

// standardizeYida 标准化易达回调数据
func (s *CallbackStandardizer) standardizeYida(parsedData *model.ParsedCallbackData) (*model.StandardizedCallbackData, error) {
	switch parsedData.Type {
	case "order_status_changed":
		return s.standardizeYidaStatusPush(parsedData)
	case "billing_updated":
		return s.standardizeYidaBillingPush(parsedData)
	case "pickup_info_updated":
		return s.standardizeYidaPickupPush(parsedData)
	case "order_changed":
		return s.standardizeYidaOrderChange(parsedData)
	case "ticket_replied":
		return s.standardizeYidaTicketReply(parsedData)
	default:
		return nil, fmt.Errorf("未知的易达回调类型: %s", parsedData.Type)
	}
}

// standardizeYidaStatusPush 标准化易达状态推送
func (s *CallbackStandardizer) standardizeYidaStatusPush(parsedData *model.ParsedCallbackData) (*model.StandardizedCallbackData, error) {
	contextObj, ok := parsedData.Data.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("易达状态数据格式错误")
	}

	ydOrderStatus, _ := contextObj["ydOrderStatus"].(string)
	ydOrderStatusDesc, _ := contextObj["ydOrderStatusDesc"].(string)

	// 易达状态推送的pushType为1
	statusResult := s.statusMapper.MapYidaStatus(1, ydOrderStatus, contextObj)

	eventData := &model.OrderStatusChangedData{
		NewStatus:  statusResult.Status,
		StatusDesc: statusResult.GetDescription(),
		UpdateTime: util.NowBeijing(),
		Extra: map[string]interface{}{
			"provider_status": ydOrderStatus,
			"provider_desc":   ydOrderStatusDesc,
			"raw_status":      statusResult.RawStatus,
			"status_extra":    statusResult.Extra,
		},
	}

	return &model.StandardizedCallbackData{
		EventType:       model.EventTypeOrderStatusChanged,
		OrderNo:         parsedData.OrderNo,
		CustomerOrderNo: parsedData.CustomerOrderNo,
		TrackingNo:      parsedData.TrackingNo,
		Provider:        model.CallbackProviderYida,
		Timestamp:       util.NowBeijing(),
		Data:            eventData,
	}, nil
}

// standardizeYidaBillingPush 标准化易达计费推送
func (s *CallbackStandardizer) standardizeYidaBillingPush(parsedData *model.ParsedCallbackData) (*model.StandardizedCallbackData, error) {
	contextObj, ok := parsedData.Data.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("易达计费数据格式错误")
	}

	// 处理两种格式：详细格式和简化格式
	var realWeight, realVolume, calcFeeWeight float64
	var totalFee float64

	// 尝试解析详细格式的realWeight
	if rw, ok := contextObj["realWeight"].(float64); ok {
		realWeight = rw
	} else if weightStr, ok := contextObj["weight"].(string); ok {
		// 简化格式的weight是字符串
		realWeight, _ = strconv.ParseFloat(weightStr, 64)
	}

	// 尝试解析详细格式的realVolume
	if rv, ok := contextObj["realVolume"].(float64); ok {
		realVolume = rv
	} else if volumeStr, ok := contextObj["volume"].(string); ok {
		// 简化格式的volume是字符串，单位是CM³，需要转换为M³
		volumeCm3, _ := strconv.ParseFloat(volumeStr, 64)
		realVolume = volumeCm3 / 1000000 // CM³转M³
	}

	// 尝试解析calcFeeWeight
	if cfw, ok := contextObj["calcFeeWeight"].(float64); ok {
		calcFeeWeight = cfw
	} else {
		// 简化格式没有calcFeeWeight，使用realWeight
		calcFeeWeight = realWeight
	}

	// 尝试解析简化格式的totalFee
	if totalFeeStr, ok := contextObj["totalFee"].(string); ok {
		totalFee, _ = strconv.ParseFloat(totalFeeStr, 64)
	}

	// 处理费用明细
	var feeDetails []model.FeeDetail
	if feeBlockList, ok := contextObj["feeBlockList"].([]interface{}); ok {
		for _, feeBlock := range feeBlockList {
			if feeMap, ok := feeBlock.(map[string]interface{}); ok {
				feeType, _ := feeMap["type"].(float64)
				feeName, _ := feeMap["name"].(string)
				feeAmount, _ := feeMap["fee"].(float64)

				// 映射费用类型（基于易达官方文档）
				var mappedType string
				switch int(feeType) {
				case 0:
					mappedType = "freight" // 🔥 修复：快递运费
				case 1:
					mappedType = "insurance" // 保价费
				case 2:
					mappedType = "holiday" // 春节加派费
				case 3:
					mappedType = "package" // 耗材费
				case 10:
					// 根据费用名称判断是超重费还是逆向费用
					if strings.Contains(feeName, "超重") || strings.Contains(feeName, "重量") {
						mappedType = "overweight" // 超重费用
					} else if strings.Contains(feeName, "超轻") {
						mappedType = "underweight" // 超轻费用
					} else {
						mappedType = "return_fee" // 🔥 修复：易达退回费用映射
					}
				case 100:
					mappedType = "other" // 其他费用
				default:
					mappedType = "other" // 未知费用类型
				}

				feeDetails = append(feeDetails, model.FeeDetail{
					FeeType:   mappedType,
					FeeDesc:   feeName,
					Amount:    feeAmount,
					PayStatus: "paid",
					// 兼容旧字段
					Type: strconv.Itoa(int(feeType)),
					Name: feeName,
				})
			}
		}
	}

	// 计算总费用（如果有费用明细，重新计算；否则使用简化格式的totalFee）
	if len(feeDetails) > 0 {
		totalFee = 0 // 重置为0，重新计算
		for _, fee := range feeDetails {
			totalFee += fee.Amount
		}
	} else if totalFee > 0 {
		// 简化格式：创建默认费用明细
		feeDetails = append(feeDetails, model.FeeDetail{
			FeeType:   "freight",
			FeeDesc:   "运费",
			Amount:    totalFee,
			PayStatus: "paid",
			// 兼容旧字段
			Type: "0",
			Name: "运费",
		})
	}

	// 构建计费信息
	var pricingInfo *model.PricingInfo
	if totalFee > 0 {
		pricingInfo = &model.PricingInfo{
			FirstWeight:   1.0, // 默认首重1kg
			FirstPrice:    totalFee,
			ContinuePrice: 0,
			Discount:      100, // 默认无折扣
			DiscountFee:   0,
		}
	}

	// 计算正确的计费重量（向上取整）
	chargedWeight := s.calculateChargedWeight(realWeight, realVolume)

	// 🔥 修复：快递行业标准，计费重量最小为1kg
	if chargedWeight < 1.0 {
		chargedWeight = 1.0
	}

	// 如果易达提供了计费重量且大于计算值，使用易达的值
	if calcFeeWeight > chargedWeight {
		chargedWeight = calcFeeWeight
	}

	eventData := &model.BillingUpdatedData{
		Weight:        realWeight,
		Volume:        realVolume,
		Cost:          totalFee, // 易达的总费用作为成本
		ChargedWeight: chargedWeight,
		TotalFee:      totalFee,
		OfficialFee:   totalFee, // 易达没有单独的官方价格，使用总费用
		PackageCount:  1,        // 默认1个包裹
		FeeDetails:    feeDetails,
		PricingInfo:   pricingInfo,
		UpdateTime:    util.NowBeijing(),
	}

	// 🔥 修复：如果parsedData.OrderNo为空，尝试从contextObj中提取
	orderNo := parsedData.OrderNo
	if orderNo == "" {
		if contextOrderNo, ok := contextObj["orderNo"].(string); ok {
			orderNo = contextOrderNo
		} else if contextWaybillNo, ok := contextObj["waybillNo"].(string); ok {
			orderNo = contextWaybillNo
		}
	}

	return &model.StandardizedCallbackData{
		EventType:       model.EventTypeBillingUpdated,
		OrderNo:         orderNo,
		CustomerOrderNo: parsedData.CustomerOrderNo,
		TrackingNo:      parsedData.TrackingNo,
		Provider:        model.CallbackProviderYida,
		Timestamp:       util.NowBeijing(),
		Data:            eventData,
	}, nil
}

// standardizeYidaPickupPush 标准化易达揽收推送
func (s *CallbackStandardizer) standardizeYidaPickupPush(parsedData *model.ParsedCallbackData) (*model.StandardizedCallbackData, error) {
	contextObj, ok := parsedData.Data.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("易达揽收数据格式错误")
	}

	courierName, _ := contextObj["courierName"].(string)
	courierPhone, _ := contextObj["courierPhone"].(string)
	pickUpCode, _ := contextObj["pickUpCode"].(string)
	siteName, _ := contextObj["siteName"].(string)

	courierInfo := &model.CourierInfo{
		Name:    courierName,
		Phone:   courierPhone,
		Station: siteName,
	}

	// 揽收推送表示快递员已接单，统一映射为已分配状态
	eventData := &model.OrderStatusChangedData{
		NewStatus:   model.OrderStatusAssigned,
		StatusDesc:  model.GetOrderStatusDesc(model.OrderStatusAssigned),
		UpdateTime:  util.NowBeijing(),
		CourierInfo: courierInfo,
		Extra: map[string]interface{}{
			"pickup_code": pickUpCode,
			"site_name":   siteName,
		},
	}

	return &model.StandardizedCallbackData{
		EventType:       model.EventTypeOrderStatusChanged,
		OrderNo:         parsedData.OrderNo,
		CustomerOrderNo: parsedData.CustomerOrderNo,
		TrackingNo:      parsedData.TrackingNo,
		Provider:        model.CallbackProviderYida,
		Timestamp:       util.NowBeijing(),
		Data:            eventData,
	}, nil
}

// standardizeYidaOrderChange 标准化易达订单变更
func (s *CallbackStandardizer) standardizeYidaOrderChange(parsedData *model.ParsedCallbackData) (*model.StandardizedCallbackData, error) {
	// 易达订单变更的处理逻辑
	return &model.StandardizedCallbackData{
		EventType:       model.EventTypeOrderStatusChanged,
		OrderNo:         parsedData.OrderNo,
		CustomerOrderNo: parsedData.CustomerOrderNo,
		TrackingNo:      parsedData.TrackingNo,
		Provider:        model.CallbackProviderYida,
		Timestamp:       util.NowBeijing(),
		Data:            parsedData.Data,
	}, nil
}

// standardizeYidaTicketReply 标准化易达工单回复
func (s *CallbackStandardizer) standardizeYidaTicketReply(parsedData *model.ParsedCallbackData) (*model.StandardizedCallbackData, error) {
	ticketData, ok := parsedData.Data.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("易达工单数据格式错误")
	}

	taskNo, _ := ticketData["taskNo"].(string)
	status, _ := ticketData["status"].(int)
	content, _ := ticketData["content"].(string)
	urls, _ := ticketData["urls"].(string)
	ticketType, _ := ticketData["type"].(int)

	eventData := &model.TicketRepliedData{
		TicketID:   taskNo,
		Status:     fmt.Sprintf("%d", status),
		Reply:      content,
		ReplyTime:  util.NowBeijing(),
		TicketType: fmt.Sprintf("%d", ticketType),
	}

	// 处理附件URL
	if urls != "" {
		eventData.Attachments = []string{urls}
	}

	return &model.StandardizedCallbackData{
		EventType:       model.EventTypeTicketReplied,
		OrderNo:         parsedData.OrderNo,
		CustomerOrderNo: parsedData.CustomerOrderNo,
		TrackingNo:      parsedData.TrackingNo,
		Provider:        model.CallbackProviderYida,
		Timestamp:       util.NowBeijing(),
		Data:            eventData,
	}, nil
}

// standardizeKuaidi100 标准化快递100回调数据
func (s *CallbackStandardizer) standardizeKuaidi100(parsedData *model.ParsedCallbackData) (*model.StandardizedCallbackData, error) {
	// 根据回调类型分别处理
	switch parsedData.Type {
	case constants.CallbackTypeOrderCallback:
		return s.standardizeKuaidi100OrderCallback(parsedData)
	case constants.CallbackTypeStatusUpdate:
		return s.standardizeKuaidi100TrackingUpdate(parsedData)
	case constants.CallbackTypeTrackingComplete:
		return s.standardizeKuaidi100TrackingComplete(parsedData)
	case constants.CallbackTypeTrackingAbort:
		return s.standardizeKuaidi100TrackingAbort(parsedData)
	default:
		return nil, fmt.Errorf("未知的快递100回调类型: %s", parsedData.Type)
	}
}

// standardizeKuaidi100OrderCallback 标准化快递100订单回调
func (s *CallbackStandardizer) standardizeKuaidi100OrderCallback(parsedData *model.ParsedCallbackData) (*model.StandardizedCallbackData, error) {
	// 快递100的订单回调数据处理
	// 🔥 修复：使用完整的结构体定义，包含所有可能的字段
	orderData, ok := parsedData.Data.(struct {
		OrderId       string `json:"orderId"`
		Status        int    `json:"status"`
		SentStatus    int    `json:"sentStatus"` // 🔥 新增：发送状态
		CancelMsg9    string `json:"cancelMsg9"`
		CancelMsg99   string `json:"cancelMsg99"`
		CourierName   string `json:"courierName"`
		CourierMobile string `json:"courierMobile"`
		NetTel        string `json:"netTel"`
		NetCode       string `json:"netCode"`
		Weight        string `json:"weight"`
		DefPrice      string `json:"defPrice"`
		Freight       string `json:"freight"`
		Volume        string `json:"volume"`
		ActualWeight  string `json:"actualWeight"`
		VolumeWeight  string `json:"volumeWeight"`  // 🔥 新增：体积重量
		ServiceType   string `json:"serviceType"`   // 🔥 新增：服务类型
		FirstWeight   string `json:"firstWeight"`   // 🔥 新增：首重
		FirstPrice    string `json:"firstPrice"`    // 🔥 新增：首重价格
		OverWeight    string `json:"overWeight"`    // 🔥 新增：超重
		OfficalPrice  string `json:"officalPrice"`  // 🔥 新增：官方价格
		OverPriceUnit string `json:"overPriceUnit"` // 🔥 新增：超重价格单位
		ThirdOrderId  string `json:"thirdOrderId"`  // 🔥 新增：第三方订单ID
		PayType       string `json:"payType"`       // 🔥 新增：支付类型
		PayStatus     int    `json:"payStatus"`     // 🔥 新增：支付状态
		UserCancel    bool   `json:"userCancel"`    // 🔥 新增：用户取消
		Comment       string `json:"comment"`       // 🔥 新增：备注
		UpdateTime    int64  `json:"updateTime"`    // 🔥 新增：更新时间
		ExtraInfo     string `json:"extraInfo"`     // 🔥 新增：额外信息
		FeeDetails    []struct {
			FeeType     string      `json:"feeType"`     // 费用类型
			FeeDesc     string      `json:"feeDesc"`     // 费用描述
			Amount      string      `json:"amount"`      // 金额
			PayStatus   interface{} `json:"payStatus"`   // 支付状态（可能是string或int）
			PayTaskId   string      `json:"payTaskId"`   // 支付任务ID
			OrderBaseId interface{} `json:"orderBaseId"` // 订单基础ID（可能是string或int）
		} `json:"feeDetails"`
		PrintTaskId string `json:"printTaskId"`
		Label       string `json:"label"`
		PickupCode  string `json:"pickupCode"`
		PollToken   string `json:"pollToken"`
	})

	if !ok {
		return nil, fmt.Errorf("快递100订单数据格式错误")
	}

	// 根据状态判断事件类型
	var eventType string
	var eventData interface{}

	// 根据状态码和数据内容精确判断事件类型
	eventType = s.determineKuaidi100EventType(orderData.Status, orderData)

	// 🔥 修复：先检查费用字段有效性，决定事件类型
	if eventType == model.EventTypeBillingUpdated {
		// 🔥 关键修复：如果费用字段都为空，转换为状态变更事件
		if orderData.Freight == "" && orderData.DefPrice == "" && len(orderData.FeeDetails) == 0 {
			s.logger.Warn("⚠️ 快递100回调费用字段为空，转换为状态变更事件",
				zap.String("order_no", parsedData.OrderNo),
				zap.Int("status", orderData.Status),
				zap.String("freight", orderData.Freight),
				zap.String("defPrice", orderData.DefPrice))

			// 转换为状态变更事件
			eventType = model.EventTypeOrderStatusChanged
		}
	}

	if eventType == model.EventTypeBillingUpdated {
		// 计费更新事件
		// 🔥 修复：根据快递100官方文档正确映射字段
		// weight = 计费重量，actualWeight = 称重重量（实际重量）
		chargedWeight, _ := strconv.ParseFloat(orderData.Weight, 64)      // 计费重量
		actualWeight, _ := strconv.ParseFloat(orderData.ActualWeight, 64) // 实际重量

		// 如果没有实际重量数据，使用计费重量作为实际重量
		if actualWeight == 0 {
			actualWeight = chargedWeight
		}

		// 解析费用字段
		freight, _ := strconv.ParseFloat(orderData.Freight, 64)
		volume, _ := strconv.ParseFloat(orderData.Volume, 64)
		defPrice, _ := strconv.ParseFloat(orderData.DefPrice, 64)

		var feeDetails []model.FeeDetail
		var totalFee float64
		for _, fee := range orderData.FeeDetails {
			amount, _ := strconv.ParseFloat(fee.Amount, 64)

			// 识别和映射费用类型
			feeType := fee.FeeType
			if strings.Contains(fee.FeeDesc, "超重") || strings.Contains(fee.FeeType, "overweight") {
				feeType = "overweight"
			} else if strings.Contains(fee.FeeDesc, "超轻") || strings.Contains(fee.FeeType, "underweight") {
				feeType = "underweight"
			} else if fee.FeeType == "RETURNFEE" || strings.Contains(fee.FeeDesc, "逆向费") || strings.Contains(fee.FeeDesc, "退回费") {
				feeType = "return_fee" // 🔥 新增：快递100退回费用映射
			} else if fee.FeeType == "PACKAGINGFEE" || strings.Contains(fee.FeeDesc, "包装") {
				feeType = "packaging_fee" // 🔥 新增：快递100包装费映射
			} else if fee.FeeType == "INSURANCEFEE" || strings.Contains(fee.FeeDesc, "保险") {
				feeType = "insurance_fee" // 🔥 新增：快递100保险费映射
			} else if fee.FeeType == "DELIVERYFEE" || strings.Contains(fee.FeeDesc, "配送") {
				feeType = "delivery_fee" // 🔥 新增：快递100配送费映射
			}

			// 🔥 修复：处理PayStatus的类型转换（可能是string或int）
			var payStatus string
			if fee.PayStatus != nil {
				switch v := fee.PayStatus.(type) {
				case string:
					payStatus = v
				case int:
					payStatus = strconv.Itoa(v)
				case float64:
					payStatus = strconv.Itoa(int(v))
				default:
					payStatus = fmt.Sprintf("%v", v)
				}
			} else {
				payStatus = "unknown"
			}

			feeDetails = append(feeDetails, model.FeeDetail{
				FeeType:   feeType,
				FeeDesc:   fee.FeeDesc,
				Amount:    amount,
				PayStatus: payStatus,
				// 兼容旧字段
				Type: fee.FeeType,
				Name: fee.FeeDesc,
			})
			totalFee += amount
		}

		// 🔥 修复：统一费用明细处理逻辑，与易达保持一致
		// 🔥 修复：正确的费用计算逻辑 - 运费 + 费用明细
		totalFee = freight // 先加上基础运费

		// 如果有费用明细，加上所有明细费用
		if len(feeDetails) > 0 {
			for _, fee := range feeDetails {
				totalFee += fee.Amount
			}
		}

		// 如果有运费，确保运费明细存在
		if freight > 0 {
			// 检查是否已有运费明细
			hasFreightDetail := false
			for _, fee := range feeDetails {
				if fee.FeeType == "freight" || strings.Contains(fee.FeeDesc, "运费") {
					hasFreightDetail = true
					break
				}
			}

			// 如果没有运费明细，添加运费明细
			if !hasFreightDetail {
				feeDetails = append(feeDetails, model.FeeDetail{
					FeeType:   "freight",
					FeeDesc:   "运费",
					Amount:    freight,
					PayStatus: "paid",
					// 兼容旧字段
					Type: "freight",
					Name: "运费",
				})
			}
		}

		// 解析官方价格（已在上面声明）
		// defPrice 已在上面声明，这里不需要重复声明

		// 构建计费信息
		var pricingInfo *model.PricingInfo
		if totalFee > 0 {
			pricingInfo = &model.PricingInfo{
				FirstWeight:   1.0, // 默认首重1kg
				FirstPrice:    totalFee,
				ContinuePrice: 0,
				Discount:      100, // 默认无折扣
				DiscountFee:   0,
			}
		}

		// 🔥 修复：快递100的weight字段就是计费重量，不需要重新计算
		// 但要确保最小值为1kg（快递行业标准）
		if chargedWeight < 1.0 {
			chargedWeight = 1.0
		}

		eventData = &model.BillingUpdatedData{
			Weight:        actualWeight, // 实际重量
			Volume:        volume,
			Cost:          freight,       // 运费作为成本
			ChargedWeight: chargedWeight, // 计费重量
			TotalFee:      totalFee,
			OfficialFee:   defPrice, // 快递100的标准运费
			PackageCount:  1,        // 默认1个包裹
			FeeDetails:    feeDetails,
			PricingInfo:   pricingInfo,
			UpdateTime:    util.NowBeijing(),
		}
	} else {
		// 状态变更事件

		// 构建数据映射
		dataMap := map[string]interface{}{
			"weight":       orderData.Weight,
			"freight":      orderData.Freight,
			"volume":       orderData.Volume,
			"actualWeight": orderData.ActualWeight,
			"feeDetails":   orderData.FeeDetails,
		}

		statusResult := s.statusMapper.MapKuaidi100Status(orderData.Status, dataMap)

		courierInfo := &model.CourierInfo{
			Name:  orderData.CourierName,
			Phone: orderData.CourierMobile,
		}

		eventData = &model.OrderStatusChangedData{
			NewStatus:   statusResult.Status,
			StatusDesc:  statusResult.GetDescription(),
			UpdateTime:  util.NowBeijing(),
			CourierInfo: courierInfo,
			Extra: map[string]interface{}{
				"provider_status": orderData.Status,
				"pickup_code":     orderData.PickupCode,
				"net_tel":         orderData.NetTel,
				"net_code":        orderData.NetCode,
				"cancel_msg9":     orderData.CancelMsg9,
				"cancel_msg99":    orderData.CancelMsg99,
				"raw_status":      statusResult.RawStatus,
				"status_extra":    statusResult.Extra,
			},
		}
	}

	return &model.StandardizedCallbackData{
		EventType:       eventType,
		OrderNo:         parsedData.OrderNo,
		CustomerOrderNo: parsedData.CustomerOrderNo,
		TrackingNo:      parsedData.TrackingNo,
		Provider:        model.CallbackProviderKuaidi100,
		Timestamp:       util.NowBeijing(),
		Data:            eventData,
	}, nil
}

// standardizeKuaidi100TrackingUpdate 标准化快递100轨迹更新推送
func (s *CallbackStandardizer) standardizeKuaidi100TrackingUpdate(parsedData *model.ParsedCallbackData) (*model.StandardizedCallbackData, error) {
	// 解析轨迹推送数据
	trackingData, ok := parsedData.Data.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("快递100轨迹推送数据格式错误")
	}

	lastResult, ok := trackingData["lastResult"].(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("快递100轨迹推送lastResult数据格式错误")
	}

	// 提取轨迹信息
	trackingNo := ""
	if nu, ok := lastResult["nu"].(string); ok {
		trackingNo = nu
	}

	state := ""
	if stateStr, ok := lastResult["state"].(string); ok {
		state = stateStr
	}

	// 提取轨迹明细
	var trackingDetails []map[string]interface{}
	if data, ok := lastResult["data"].([]interface{}); ok {
		for _, item := range data {
			if detail, ok := item.(map[string]interface{}); ok {
				trackingDetails = append(trackingDetails, detail)
			}
		}
	}

	// 构建标准化数据
	standardData := map[string]interface{}{
		"tracking_no":      trackingNo,
		"status":           state,
		"status_desc":      trackingData["message"],
		"tracking_details": trackingDetails,
		"company_code":     lastResult["com"],
		"is_check":         lastResult["ischeck"],
		"update_time":      util.NowBeijing().Format(time.RFC3339),
	}

	return &model.StandardizedCallbackData{
		EventType:       "tracking_updated",
		Provider:        "kuaidi100",
		TrackingNo:      trackingNo,
		CustomerOrderNo: "", // 轨迹推送没有订单号，需要通过运单号查找
		Data:            standardData,
		Timestamp:       parsedData.Timestamp,
	}, nil
}

// standardizeKuaidi100TrackingComplete 标准化快递100轨迹完成推送
func (s *CallbackStandardizer) standardizeKuaidi100TrackingComplete(parsedData *model.ParsedCallbackData) (*model.StandardizedCallbackData, error) {
	// 解析轨迹推送数据
	trackingData, ok := parsedData.Data.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("快递100轨迹完成数据格式错误")
	}

	lastResult, ok := trackingData["lastResult"].(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("快递100轨迹完成lastResult数据格式错误")
	}

	// 提取轨迹信息
	trackingNo := ""
	if nu, ok := lastResult["nu"].(string); ok {
		trackingNo = nu
	}

	// 提取最后的轨迹明细
	var lastTrackingDetail map[string]interface{}
	if data, ok := lastResult["data"].([]interface{}); ok && len(data) > 0 {
		if detail, ok := data[len(data)-1].(map[string]interface{}); ok {
			lastTrackingDetail = detail
		}
	}

	// 构建标准化数据
	standardData := map[string]interface{}{
		"tracking_no":     trackingNo,
		"status":          "delivered",
		"status_desc":     "已签收",
		"final_status":    lastResult["state"],
		"is_check":        lastResult["ischeck"],
		"last_tracking":   lastTrackingDetail,
		"completion_time": util.NowBeijing().Format(time.RFC3339),
		"company_code":    lastResult["com"],
	}

	return &model.StandardizedCallbackData{
		EventType:       "tracking_completed",
		Provider:        "kuaidi100",
		TrackingNo:      trackingNo,
		CustomerOrderNo: "", // 轨迹推送没有订单号，需要通过运单号查找
		Data:            standardData,
		Timestamp:       parsedData.Timestamp,
	}, nil
}

// standardizeKuaidi100TrackingAbort 标准化快递100轨迹中止推送
func (s *CallbackStandardizer) standardizeKuaidi100TrackingAbort(parsedData *model.ParsedCallbackData) (*model.StandardizedCallbackData, error) {
	// 解析轨迹推送数据
	trackingData, ok := parsedData.Data.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("快递100轨迹中止数据格式错误")
	}

	lastResult, ok := trackingData["lastResult"].(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("快递100轨迹中止lastResult数据格式错误")
	}

	// 提取轨迹信息
	trackingNo := ""
	if nu, ok := lastResult["nu"].(string); ok {
		trackingNo = nu
	}

	// 构建标准化数据
	standardData := map[string]interface{}{
		"tracking_no":  trackingNo,
		"status":       "abort",
		"status_desc":  "查询中止",
		"abort_reason": trackingData["message"],
		"company_code": lastResult["com"],
		"abort_time":   util.NowBeijing().Format(time.RFC3339),
	}

	return &model.StandardizedCallbackData{
		EventType:       "tracking_aborted",
		Provider:        "kuaidi100",
		TrackingNo:      trackingNo,
		CustomerOrderNo: "", // 轨迹推送没有订单号，需要通过运单号查找
		Data:            standardData,
		Timestamp:       parsedData.Timestamp,
	}, nil
}

// determineKuaidi100EventType 根据快递100状态码和数据内容精确判断事件类型
func (s *CallbackStandardizer) determineKuaidi100EventType(status int, orderData interface{}) string {
	// 类型断言获取订单数据（🔥 修复：使用与主函数相同的结构体定义）
	data, ok := orderData.(struct {
		OrderId       string `json:"orderId"`
		Status        int    `json:"status"`
		SentStatus    int    `json:"sentStatus"` // 🔥 新增：发送状态
		CancelMsg9    string `json:"cancelMsg9"`
		CancelMsg99   string `json:"cancelMsg99"`
		CourierName   string `json:"courierName"`
		CourierMobile string `json:"courierMobile"`
		NetTel        string `json:"netTel"`
		NetCode       string `json:"netCode"`
		Weight        string `json:"weight"`
		DefPrice      string `json:"defPrice"`
		Freight       string `json:"freight"`
		Volume        string `json:"volume"`
		ActualWeight  string `json:"actualWeight"`
		VolumeWeight  string `json:"volumeWeight"`  // 🔥 新增：体积重量
		ServiceType   string `json:"serviceType"`   // 🔥 新增：服务类型
		FirstWeight   string `json:"firstWeight"`   // 🔥 新增：首重
		FirstPrice    string `json:"firstPrice"`    // 🔥 新增：首重价格
		OverWeight    string `json:"overWeight"`    // 🔥 新增：超重
		OfficalPrice  string `json:"officalPrice"`  // 🔥 新增：官方价格
		OverPriceUnit string `json:"overPriceUnit"` // 🔥 新增：超重价格单位
		ThirdOrderId  string `json:"thirdOrderId"`  // 🔥 新增：第三方订单ID
		PayType       string `json:"payType"`       // 🔥 新增：支付类型
		PayStatus     int    `json:"payStatus"`     // 🔥 新增：支付状态
		UserCancel    bool   `json:"userCancel"`    // 🔥 新增：用户取消
		Comment       string `json:"comment"`       // 🔥 新增：备注
		UpdateTime    int64  `json:"updateTime"`    // 🔥 新增：更新时间
		ExtraInfo     string `json:"extraInfo"`     // 🔥 新增：额外信息
		FeeDetails    []struct {
			FeeType     string      `json:"feeType"`     // 费用类型
			FeeDesc     string      `json:"feeDesc"`     // 费用描述
			Amount      string      `json:"amount"`      // 金额
			PayStatus   interface{} `json:"payStatus"`   // 支付状态（可能是string或int）
			PayTaskId   string      `json:"payTaskId"`   // 支付任务ID
			OrderBaseId interface{} `json:"orderBaseId"` // 订单基础ID（可能是string或int）
		} `json:"feeDetails"`
		PrintTaskId string `json:"printTaskId"`
		Label       string `json:"label"`
		PickupCode  string `json:"pickupCode"`
		PollToken   string `json:"pollToken"`
	})
	if !ok {
		// 如果类型断言失败，默认返回状态变更
		return model.EventTypeOrderStatusChanged
	}

	// 检查是否包含计费信息
	hasBillingInfo := len(data.FeeDetails) > 0 ||
		data.Weight != "" ||
		data.ActualWeight != "" ||
		data.Freight != ""

	// 根据状态码精确判断事件类型
	switch status {
	case 155: // 修改重量 - 总是计费更新
		return model.EventTypeBillingUpdated
	case 0, 1, 2: // 下单成功、已接单、收件中 - 如果有计费信息则为计费更新，否则为状态变更
		if hasBillingInfo {
			return model.EventTypeBillingUpdated
		}
		return model.EventTypeOrderStatusChanged
	case 10: // 已取件 - 如果有计费信息则为计费更新，否则为状态变更
		if hasBillingInfo {
			return model.EventTypeBillingUpdated
		}
		return model.EventTypeOrderStatusChanged
	case 101: // 运输中 - 🔥 修复：如果包含费用信息则为计费更新
		if hasBillingInfo {
			return model.EventTypeBillingUpdated
		}
		return model.EventTypeOrderStatusChanged
	case 400: // 派送中 - 状态变更
		return model.EventTypeOrderStatusChanged
	case 13, 14: // 已签收、异常签收 - 状态变更
		return model.EventTypeOrderStatusChanged
	case 9, 99: // 用户主动取消、订单已取消 - 状态变更
		return model.EventTypeOrderStatusChanged
	case 15: // 已结算 - 计费更新
		return model.EventTypeBillingUpdated
	case 200: // 已出单 - 状态变更
		return model.EventTypeOrderStatusChanged
	case 201: // 出单失败 - 状态变更
		return model.EventTypeOrderStatusChanged
	case 610: // 下单失败 - 状态变更
		return model.EventTypeOrderStatusChanged
	default:
		// 未知状态码，根据是否有计费信息判断
		if hasBillingInfo {
			return model.EventTypeBillingUpdated
		}
		return model.EventTypeOrderStatusChanged
	}
}

// calculateChargedWeight 计算计费重量（向上取整）- 兼容旧方法
func (s *CallbackStandardizer) calculateChargedWeight(actualWeight, volume float64) float64 {
	// 🔥 修复：云通API返回的Volume单位是立方厘米(cm³)，需要先转换为立方米(m³)
	// 根据云通官方文档：Volume单位为立方厘米
	volumeInM3 := volume / 1000000 // cm³ → m³ (1m³ = 1,000,000cm³)

	// 计算体积重量（一般按1立方米=167kg计算，但这里使用更常见的200kg）
	volumeWeight := volumeInM3 * 200

	// 取实际重量和体积重量的较大值
	chargedWeight := actualWeight
	if volumeWeight > actualWeight {
		chargedWeight = volumeWeight
	}

	// 向上取整到最近的整数
	return math.Ceil(chargedWeight)
}

// calculateChargedWeightWithExpressCode 根据快递公司代码计算计费重量
// 严格从数据库获取抛比配置，无默认值逻辑
func (s *CallbackStandardizer) calculateChargedWeightWithExpressCode(expressCode string, actualWeight, volumeCm3 float64) float64 {
	// 如果没有快递公司代码，报错而不是使用默认计算
	if expressCode == "" {
		s.logger.Error("计算计费重量时缺少快递公司代码")
		panic("计算计费重量时缺少快递公司代码。请确保回调数据包含正确的快递公司信息")
	}

	// 严格从数据库获取快递公司配置，无默认值
	if s.mappingService == nil {
		s.logger.Error("映射服务未初始化", zap.String("express_code", expressCode))
		panic("映射服务未初始化，无法获取快递公司抛比配置")
	}

	ctx := context.Background()
	volumeRatio, err := s.mappingService.GetVolumeWeightRatio(ctx, expressCode)
	if err != nil {
		s.logger.Error("获取快递公司抛比配置失败",
			zap.String("express_code", expressCode),
			zap.Error(err))
		panic(fmt.Sprintf("获取快递公司 %s 抛比配置失败: %v。请在数据库 express_companies 表中配置正确的 volume_weight_ratio 值", expressCode, err))
	}

	s.logger.Debug("获取快递公司抛比配置成功",
		zap.String("express_code", expressCode),
		zap.Int("volume_ratio", volumeRatio))

	// 如果没有体积信息，直接返回实际重量（向上取整）
	if volumeCm3 <= 0 {
		return math.Ceil(actualWeight)
	}

	// 计算体积重量
	volumeWeight := volumeCm3 / float64(volumeRatio)

	// 取实际重量和体积重量的较大值
	chargedWeight := math.Max(actualWeight, volumeWeight)

	// 向上取整到最近的整数（快递行业标准）
	result := math.Ceil(chargedWeight)

	fmt.Printf("🔧 体积重量计算 - 快递: %s, 抛比: %d, 实际重量: %.2fkg, 体积: %.0fcm³, 体积重量: %.2fkg, 计费重量: %.0fkg\n",
		expressCode, volumeRatio, actualWeight, volumeCm3, volumeWeight, result)

	return result
}

// standardizeCainiao 标准化菜鸟裹裹回调数据
func (s *CallbackStandardizer) standardizeCainiao(parsedData *model.ParsedCallbackData) (*model.StandardizedCallbackData, error) {
	// 从解析后的数据中提取菜鸟特有的信息
	dataMap, ok := parsedData.Data.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("菜鸟裹裹回调数据格式错误")
	}

	eventType, _ := dataMap["event_type"].(string)
	if eventType == "" {
		return nil, fmt.Errorf("菜鸟裹裹事件类型不能为空")
	}

	// 提取外层订单信息
	externalOrder, _ := dataMap["external_order"].(map[string]interface{})
	orderEvent, _ := dataMap["order_event"].(map[string]interface{})

	// 🔥 根据事件类型进行标准化处理
	switch eventType {
	case "CREATE_ORDER":
		return s.standardizeCainiaoCreateOrder(parsedData, dataMap, externalOrder, orderEvent)
	case "SEEK_DELIVERY_SUCCESS":
		return s.standardizeCainiaoSeekDeliverySuccess(parsedData, dataMap, externalOrder, orderEvent)
	case "OUT_ORDER_COURIER_UPDATE":
		return s.standardizeCainiaoCourierUpdate(parsedData, dataMap, externalOrder, orderEvent)
	case "COURIER_CHECK_BILL_SUCCESS":
		return s.standardizeCainiaoBillingUpdate(parsedData, dataMap, externalOrder, orderEvent)
	case "PAY_SUCCESS":
		return s.standardizeCainiaoPaySuccess(parsedData, dataMap, externalOrder, orderEvent)
	case "GOT_SUCCESS":
		return s.standardizeCainiaoGotSuccess(parsedData, dataMap, externalOrder, orderEvent)
	case "FINISH_ORDER":
		return s.standardizeCainiaoFinishOrder(parsedData, dataMap, externalOrder, orderEvent)
	case "ACCEPT":
		return s.standardizeCainiaoLogisticsEvent(parsedData, dataMap, externalOrder, orderEvent, model.OrderStatusPickedUp, "已揽件")
	case "TRANSPORT":
		return s.standardizeCainiaoLogisticsEvent(parsedData, dataMap, externalOrder, orderEvent, model.OrderStatusInTransit, "运输中")
	case "DELIVERING":
		return s.standardizeCainiaoLogisticsEvent(parsedData, dataMap, externalOrder, orderEvent, model.OrderStatusOutForDelivery, "派件中")
	case "SIGN":
		return s.standardizeCainiaoLogisticsEvent(parsedData, dataMap, externalOrder, orderEvent, model.OrderStatusDelivered, "已签收")
	case "CANCEL_ORDER":
		return s.standardizeCainiaoCancelOrder(parsedData, dataMap, externalOrder, orderEvent)
	case "MODIFY_EXPECT_GOT_TIME_SUCCESS":
		return s.standardizeCainiaoModifyTime(parsedData, dataMap, externalOrder, orderEvent)
	case "CHANGE_DELIVERY_USER_SUCCESS":
		return s.standardizeCainiaoCourierChange(parsedData, dataMap, externalOrder, orderEvent)
	case "UPLOAD_MAIL_NO_SUCCESS":
		return s.standardizeCainiaoUploadMailNo(parsedData, dataMap, externalOrder, orderEvent)
	case "FAILED":
		return s.standardizeCainiaoLogisticsEvent(parsedData, dataMap, externalOrder, orderEvent, model.OrderStatusException, "温馨提示中")
	case "REJECT":
		// 🔥 修复：拒签应该映射为异常签收状态，而不是异常状态
		return s.standardizeCainiaoLogisticsEvent(parsedData, dataMap, externalOrder, orderEvent, model.OrderStatusDeliveredAbnormal, "拒签")
	case "AGENT_SIGN":
		return s.standardizeCainiaoLogisticsEvent(parsedData, dataMap, externalOrder, orderEvent, model.OrderStatusAwaitingPickup, "待取件")
	case "STA_DELIVERING":
		return s.standardizeCainiaoLogisticsEvent(parsedData, dataMap, externalOrder, orderEvent, model.OrderStatusOutForDelivery, "驿站派送中")
	case "ORDER_TRANSER":
		return s.standardizeCainiaoLogisticsEvent(parsedData, dataMap, externalOrder, orderEvent, model.OrderStatusForwarded, "已转单")
	case "REVERSE_RETURN":
		return s.standardizeCainiaoLogisticsEvent(parsedData, dataMap, externalOrder, orderEvent, model.OrderStatusReturned, "退货返回")
	default:
		// 对于未知事件类型，映射为异常状态
		return s.standardizeCainiaoUnknownEvent(parsedData, dataMap, externalOrder, orderEvent, eventType)
	}
}

// standardizeCainiaoCreateOrder 标准化菜鸟创建订单事件
func (s *CallbackStandardizer) standardizeCainiaoCreateOrder(parsedData *model.ParsedCallbackData, dataMap map[string]interface{}, externalOrder map[string]interface{}, orderEvent map[string]interface{}) (*model.StandardizedCallbackData, error) {
	// 提取事件数据
	eventData, _ := orderEvent["eventData"].(map[string]interface{})

	// 提取运单号
	var trackingNo string
	if mailNo, ok := eventData["mailNo"].(string); ok {
		trackingNo = mailNo
	}

	// 构建状态变更数据
	statusData := &model.OrderStatusChangedData{
		NewStatus:  model.OrderStatusSubmitted,
		StatusDesc: model.SystemOrderStatus(model.OrderStatusSubmitted).GetLabel(), // 使用统一状态标签
		UpdateTime: util.NowBeijing(),
		Extra: map[string]interface{}{
			"event_type":    "CREATE_ORDER",
			"tracking_no":   trackingNo,
			"provider_data": eventData,
		},
	}

	return &model.StandardizedCallbackData{
		EventType:       model.EventTypeOrderStatusChanged,
		OrderNo:         parsedData.OrderNo,
		CustomerOrderNo: parsedData.CustomerOrderNo,
		TrackingNo:      trackingNo,
		Provider:        model.CallbackProviderCainiao,
		Timestamp:       util.NowBeijing(),
		Data:            statusData,
	}, nil
}

// standardizeCainiaoSeekDeliverySuccess 标准化菜鸟寻求运力成功事件
func (s *CallbackStandardizer) standardizeCainiaoSeekDeliverySuccess(parsedData *model.ParsedCallbackData, dataMap map[string]interface{}, externalOrder map[string]interface{}, orderEvent map[string]interface{}) (*model.StandardizedCallbackData, error) {
	// 提取事件数据
	eventData, _ := orderEvent["eventData"].(map[string]interface{})

	// 🔥 修复：增强快递员信息提取
	var courierInfo *model.CourierInfo
	courierName := s.getStringFromMap(eventData, "courierName")
	courierMobile := s.getStringFromMap(eventData, "courierMobile")
	courierCompany := s.getStringFromMap(eventData, "courierCompany")
	lpCode := s.getStringFromMap(eventData, "lpCode")
	gotCode := s.getStringFromMap(eventData, "gotCode")

	if courierName != "" {
		courierInfo = &model.CourierInfo{
			Name:       courierName,
			Phone:      courierMobile,
			Code:       lpCode,
			Station:    courierCompany,
			PickupCode: gotCode,
		}
	}

	// 🔥 修复：增强Extra字段信息
	extraData := map[string]interface{}{
		"event_type":      "SEEK_DELIVERY_SUCCESS",
		"got_code":        gotCode,
		"courier_company": courierCompany,
		"courier_name":    courierName,
		"courier_mobile":  courierMobile,
		"lp_code":         lpCode,
		"provider_data":   eventData,
	}

	// 构建状态变更数据
	statusData := &model.OrderStatusChangedData{
		NewStatus:   model.OrderStatusAssigned,
		StatusDesc:  "已分配快递员",
		UpdateTime:  util.NowBeijing(),
		CourierInfo: courierInfo,
		Extra:       extraData,
	}

	return &model.StandardizedCallbackData{
		EventType:       model.EventTypeOrderStatusChanged,
		OrderNo:         parsedData.OrderNo,
		CustomerOrderNo: parsedData.CustomerOrderNo,
		TrackingNo:      parsedData.TrackingNo,
		Provider:        model.CallbackProviderCainiao,
		Timestamp:       util.NowBeijing(),
		Data:            statusData,
	}, nil
}

// getStringFromMap 从map中安全获取字符串值
func (s *CallbackStandardizer) getStringFromMap(data map[string]interface{}, key string) string {
	if value, ok := data[key].(string); ok {
		return value
	}
	return ""
}

// getIntFromMap 从map中安全获取int（支持string/float64）
func (s *CallbackStandardizer) getIntFromMap(data map[string]interface{}, key string) int {
	if v, ok := data[key]; ok {
		switch t := v.(type) {
		case int:
			return t
		case float64:
			return int(t)
		case string:
			if n, err := strconv.Atoi(t); err == nil {
				return n
			}
		}
	}
	return 0
}

// getFloat64FromMap 从map中安全获取float64（支持string/int）
func (s *CallbackStandardizer) getFloat64FromMap(data map[string]interface{}, key string) float64 {
	if v, ok := data[key]; ok {
		switch t := v.(type) {
		case float64:
			return t
		case int:
			return float64(t)
		case string:
			if f, err := strconv.ParseFloat(t, 64); err == nil {
				return f
			}
		}
	}
	return 0
}

// getStringSliceFromMap 从map中安全获取字符串切片（支持[]any/逗号分隔string）
func (s *CallbackStandardizer) getStringSliceFromMap(data map[string]interface{}, key string) []string {
	if v, ok := data[key]; ok {
		switch t := v.(type) {
		case []string:
			return t
		case []interface{}:
			res := make([]string, 0, len(t))
			for _, it := range t {
				if s, ok := it.(string); ok {
					res = append(res, s)
				}
			}
			return res
		case string:
			if t == "" {
				return nil
			}
			parts := strings.Split(t, ",")
			res := make([]string, 0, len(parts))
			for _, p := range parts {
				p = strings.TrimSpace(p)
				if p != "" {
					res = append(res, p)
				}
			}
			return res
		}
	}
	return nil
}

// standardizeCainiaoCourierUpdate 标准化菜鸟快递员信息更新事件
func (s *CallbackStandardizer) standardizeCainiaoCourierUpdate(parsedData *model.ParsedCallbackData, dataMap map[string]interface{}, externalOrder map[string]interface{}, orderEvent map[string]interface{}) (*model.StandardizedCallbackData, error) {
	// 提取事件数据
	eventData, _ := orderEvent["eventData"].(map[string]interface{})

	// 🔥 修复：增强快递员信息提取
	var courierInfo *model.CourierInfo
	courierName := s.getStringFromMap(eventData, "courierName")
	courierMobile := s.getStringFromMap(eventData, "courierMobile")
	courierCompany := s.getStringFromMap(eventData, "courierCompany")
	lpCode := s.getStringFromMap(eventData, "lpCode")
	gotCode := s.getStringFromMap(eventData, "gotCode")

	if courierName != "" {
		courierInfo = &model.CourierInfo{
			Name:       courierName,
			Phone:      courierMobile,
			Code:       lpCode,
			Station:    courierCompany,
			PickupCode: gotCode,
		}
	}

	// 🔥 修复：增强Extra字段信息
	extraData := map[string]interface{}{
		"event_type":      "OUT_ORDER_COURIER_UPDATE",
		"courier_company": courierCompany,
		"courier_name":    courierName,
		"courier_mobile":  courierMobile,
		"lp_code":         lpCode,
		"got_code":        gotCode,
		"provider_data":   eventData,
	}

	// 构建状态变更数据
	statusData := &model.OrderStatusChangedData{
		NewStatus:   model.OrderStatusAssigned,
		StatusDesc:  "快递员信息更新",
		UpdateTime:  util.NowBeijing(),
		CourierInfo: courierInfo,
		Extra:       extraData,
	}

	return &model.StandardizedCallbackData{
		EventType:       model.EventTypeOrderStatusChanged,
		OrderNo:         parsedData.OrderNo,
		CustomerOrderNo: parsedData.CustomerOrderNo,
		TrackingNo:      parsedData.TrackingNo,
		Provider:        model.CallbackProviderCainiao,
		Timestamp:       util.NowBeijing(),
		Data:            statusData,
	}, nil
}

// standardizeCainiaoBillingUpdate 标准化菜鸟计费更新事件
func (s *CallbackStandardizer) standardizeCainiaoBillingUpdate(parsedData *model.ParsedCallbackData, dataMap map[string]interface{}, externalOrder map[string]interface{}, orderEvent map[string]interface{}) (*model.StandardizedCallbackData, error) {
	// 提取事件数据
	eventData, _ := orderEvent["eventData"].(map[string]interface{})

	// 提取计费信息
	var totalFee float64
	if basePrice, ok := eventData["basePrice"].(string); ok {
		if price, err := strconv.ParseFloat(basePrice, 64); err == nil {
			totalFee = price / 100 // 菜鸟返回的是分，需要转换为元
		}
	}

	var weight float64
	if weightStr, ok := eventData["weight"].(string); ok {
		if w, err := strconv.ParseFloat(weightStr, 64); err == nil {
			weight = w / 1000 // 菜鸟返回的是克，需要转换为千克
		}
	}

	// 🔥 关键修复：计算菜鸟的计费重量
	// 菜鸟的计费重量通常等于实际重量，因为菜鸟已经在后台处理了体积重量计算
	chargedWeight := weight
	if chargedWeight <= 0 {
		// 如果重量为0，设置最小计费重量为1kg（快递行业标准）
		chargedWeight = 1.0
	}

	// 🔥 提取其他费用信息（如果有的话）
	var cost float64
	if costStr, ok := eventData["cost"].(string); ok {
		if c, err := strconv.ParseFloat(costStr, 64); err == nil {
			cost = c / 100 // 菜鸟返回的是分，需要转换为元
		}
	}

	// 🔥 构建费用明细
	var feeDetails []model.FeeDetail
	if totalFee > 0 {
		feeDetails = append(feeDetails, model.FeeDetail{
			FeeType: "freight",
			FeeDesc: "运费",
			Amount:  totalFee,
		})
	}

	// 构建计费更新数据
	billingData := &model.BillingUpdatedData{
		Weight:        weight,        // 实际重量
		ChargedWeight: chargedWeight, // 🔥 关键修复：设置计费重量
		Cost:          cost,          // 成本费用
		TotalFee:      totalFee,      // 总费用
		FeeDetails:    feeDetails,    // 费用明细
		UpdateTime:    util.NowBeijing(),
	}

	return &model.StandardizedCallbackData{
		EventType:       model.EventTypeBillingUpdated,
		OrderNo:         parsedData.OrderNo,
		CustomerOrderNo: parsedData.CustomerOrderNo,
		TrackingNo:      parsedData.TrackingNo,
		Provider:        model.CallbackProviderCainiao,
		Timestamp:       util.NowBeijing(),
		Data:            billingData,
	}, nil
}

// standardizeCainiaoPaySuccess 标准化菜鸟支付成功事件
func (s *CallbackStandardizer) standardizeCainiaoPaySuccess(parsedData *model.ParsedCallbackData, dataMap map[string]interface{}, externalOrder map[string]interface{}, orderEvent map[string]interface{}) (*model.StandardizedCallbackData, error) {
	// 提取事件数据
	eventData, _ := orderEvent["eventData"].(map[string]interface{})

	// 构建状态变更数据
	statusData := &model.OrderStatusChangedData{
		NewStatus:  model.OrderStatusBilled,
		StatusDesc: model.SystemOrderStatus(model.OrderStatusBilled).GetLabel(), // 使用统一状态标签
		UpdateTime: util.NowBeijing(),
		Extra: map[string]interface{}{
			"event_type":    "PAY_SUCCESS",
			"pay_time":      s.getStringFromMap(eventData, "payTime"),
			"pay_status":    s.getStringFromMap(eventData, "payStatus"),
			"provider_data": eventData,
		},
	}

	return &model.StandardizedCallbackData{
		EventType:       model.EventTypeOrderStatusChanged,
		OrderNo:         parsedData.OrderNo,
		CustomerOrderNo: parsedData.CustomerOrderNo,
		TrackingNo:      parsedData.TrackingNo,
		Provider:        model.CallbackProviderCainiao,
		Timestamp:       util.NowBeijing(),
		Data:            statusData,
	}, nil
}

// standardizeCainiaoGotSuccess 标准化菜鸟取件成功事件
func (s *CallbackStandardizer) standardizeCainiaoGotSuccess(parsedData *model.ParsedCallbackData, dataMap map[string]interface{}, externalOrder map[string]interface{}, orderEvent map[string]interface{}) (*model.StandardizedCallbackData, error) {
	// 🔥 修复：GOT_SUCCESS事件没有mailNo字段，TrackingNo为空
	// 这个问题将在统一回调服务层面通过findUserAndOrderInfo方法解决
	// 该方法会通过CustomerOrderNo查找完整的订单信息，包括TrackingNo

	// 构建状态变更数据
	statusData := &model.OrderStatusChangedData{
		NewStatus:  model.OrderStatusPickedUp,
		StatusDesc: model.SystemOrderStatus(model.OrderStatusPickedUp).GetLabel(), // 使用统一状态标签
		UpdateTime: util.NowBeijing(),
		Extra: map[string]interface{}{
			"event_type":          "GOT_SUCCESS",
			"tracking_no_missing": parsedData.TrackingNo == "", // 标记TrackingNo缺失
		},
	}

	return &model.StandardizedCallbackData{
		EventType:       model.EventTypeOrderStatusChanged,
		OrderNo:         parsedData.OrderNo,
		CustomerOrderNo: parsedData.CustomerOrderNo,
		TrackingNo:      parsedData.TrackingNo, // 保持原值，由统一回调服务补充
		Provider:        model.CallbackProviderCainiao,
		Timestamp:       util.NowBeijing(),
		Data:            statusData,
	}, nil
}

// standardizeCainiaoFinishOrder 标准化菜鸟订单完结事件
// 🔥 修复：FINISH_ORDER事件应该映射为billed状态，而不是delivered状态
// 根据菜鸟官方文档：FINISH_ORDER表示"订单完结"，即订单已经回单后且订单支付成功后的状态
func (s *CallbackStandardizer) standardizeCainiaoFinishOrder(parsedData *model.ParsedCallbackData, dataMap map[string]interface{}, externalOrder map[string]interface{}, orderEvent map[string]interface{}) (*model.StandardizedCallbackData, error) {
	// 提取事件数据
	eventData, _ := orderEvent["eventData"].(map[string]interface{})

	// 提取运单号
	var trackingNo string
	if mailNo, ok := eventData["mailNo"].(string); ok {
		trackingNo = mailNo
	}

	// 构建状态变更数据
	// 🔥 修复：FINISH_ORDER应该映射为billed状态，表示订单计费完结
	statusData := &model.OrderStatusChangedData{
		NewStatus:  model.OrderStatusBilled,
		StatusDesc: model.SystemOrderStatus(model.OrderStatusBilled).GetLabel(), // 使用统一状态标签
		UpdateTime: util.NowBeijing(),
		Extra: map[string]interface{}{
			"event_type":    "FINISH_ORDER",
			"tracking_no":   trackingNo,
			"total_price":   s.getStringFromMap(eventData, "totalPrice"),
			"provider_data": eventData,
		},
	}

	return &model.StandardizedCallbackData{
		EventType:       model.EventTypeOrderStatusChanged,
		OrderNo:         parsedData.OrderNo,
		CustomerOrderNo: parsedData.CustomerOrderNo,
		TrackingNo:      trackingNo,
		Provider:        model.CallbackProviderCainiao,
		Timestamp:       util.NowBeijing(),
		Data:            statusData,
	}, nil
}

// standardizeCainiaoLogisticsEvent 标准化菜鸟物流事件
func (s *CallbackStandardizer) standardizeCainiaoLogisticsEvent(parsedData *model.ParsedCallbackData, dataMap map[string]interface{}, externalOrder map[string]interface{}, orderEvent map[string]interface{}, status string, statusDesc string) (*model.StandardizedCallbackData, error) {
	// 提取事件数据
	eventData, _ := orderEvent["eventData"].(map[string]interface{})

	// 提取运单号
	var trackingNo string
	if mailNo, ok := eventData["mailNo"].(string); ok {
		trackingNo = mailNo
	}

	// 构建状态变更数据
	statusData := &model.OrderStatusChangedData{
		NewStatus:  status,
		StatusDesc: statusDesc,
		UpdateTime: util.NowBeijing(),
		Extra: map[string]interface{}{
			"event_type":              orderEvent["eventType"],
			"tracking_no":             trackingNo,
			"logistics_action":        s.getStringFromMap(eventData, "logisticsAction"),
			"logistics_status":        s.getStringFromMap(eventData, "logisticsStatus"),
			"logistics_standard_desc": s.getStringFromMap(eventData, "logisticsStandardDesc"),
			"logistics_comp_name":     s.getStringFromMap(eventData, "logisticsCompName"),
			"logistics_occur":         s.getStringFromMap(eventData, "logisticsOccur"),
			"logistics_create":        s.getStringFromMap(eventData, "logisticsCreate"),
			"provider_data":           eventData,
		},
	}

	return &model.StandardizedCallbackData{
		EventType:       model.EventTypeOrderStatusChanged,
		OrderNo:         parsedData.OrderNo,
		CustomerOrderNo: parsedData.CustomerOrderNo,
		TrackingNo:      trackingNo,
		Provider:        model.CallbackProviderCainiao,
		Timestamp:       util.NowBeijing(),
		Data:            statusData,
	}, nil
}

// standardizeCainiaoUnknownEvent 标准化菜鸟未知事件
func (s *CallbackStandardizer) standardizeCainiaoUnknownEvent(parsedData *model.ParsedCallbackData, dataMap map[string]interface{}, externalOrder map[string]interface{}, orderEvent map[string]interface{}, eventType string) (*model.StandardizedCallbackData, error) {
	s.logger.Warn("收到未知的菜鸟事件类型",
		zap.String("event_type", eventType),
		zap.String("order_no", parsedData.CustomerOrderNo))

	// 构建状态变更数据，映射为异常状态
	statusData := &model.OrderStatusChangedData{
		NewStatus:  model.OrderStatusException,
		StatusDesc: fmt.Sprintf("未知事件类型: %s", eventType),
		UpdateTime: util.NowBeijing(),
		Extra: map[string]interface{}{
			"event_type":    eventType,
			"unknown_event": true,
			"provider_data": orderEvent,
		},
	}

	return &model.StandardizedCallbackData{
		EventType:       model.EventTypeOrderStatusChanged,
		OrderNo:         parsedData.OrderNo,
		CustomerOrderNo: parsedData.CustomerOrderNo,
		TrackingNo:      parsedData.TrackingNo,
		Provider:        model.CallbackProviderCainiao,
		Timestamp:       util.NowBeijing(),
		Data:            statusData,
	}, nil
}

// standardizeCainiaoCancelOrder 标准化菜鸟取消订单事件
func (s *CallbackStandardizer) standardizeCainiaoCancelOrder(parsedData *model.ParsedCallbackData, dataMap map[string]interface{}, externalOrder map[string]interface{}, orderEvent map[string]interface{}) (*model.StandardizedCallbackData, error) {
	// 提取事件数据
	eventData, _ := orderEvent["eventData"].(map[string]interface{})

	// 提取取消原因和时间
	var cancelReason string
	var cancelTime string

	if reason, ok := eventData["orderCancelReasonDesc"].(string); ok {
		cancelReason = reason
	}

	if cancelTimeStr, ok := eventData["orderCancelDatetime"].(string); ok {
		cancelTime = cancelTimeStr
	}

	// 如果没有取消原因，使用默认值
	if cancelReason == "" {
		cancelReason = "订单已取消"
	}

	// 构建状态变更数据
	statusData := &model.OrderStatusChangedData{
		NewStatus:  model.OrderStatusCancelled,
		StatusDesc: cancelReason,
		UpdateTime: util.NowBeijing(),
		Extra: map[string]interface{}{
			"event_type":    "CANCEL_ORDER",
			"cancel_reason": cancelReason,
			"cancel_time":   cancelTime,
			"provider_data": eventData,
		},
	}

	return &model.StandardizedCallbackData{
		EventType:       model.EventTypeOrderStatusChanged,
		OrderNo:         parsedData.OrderNo,
		CustomerOrderNo: parsedData.CustomerOrderNo,
		TrackingNo:      parsedData.TrackingNo,
		Provider:        model.CallbackProviderCainiao,
		Timestamp:       util.NowBeijing(),
		Data:            statusData,
	}, nil
}

// standardizeCainiaoModifyTime 标准化菜鸟修改期望揽收时间事件
func (s *CallbackStandardizer) standardizeCainiaoModifyTime(parsedData *model.ParsedCallbackData, dataMap map[string]interface{}, externalOrder map[string]interface{}, orderEvent map[string]interface{}) (*model.StandardizedCallbackData, error) {
	// 提取事件数据
	eventData, _ := orderEvent["eventData"].(map[string]interface{})

	// 提取修改后的时间信息
	var expectStartTime, expectEndTime string
	if startTime, ok := eventData["expectGotStartDate"].(string); ok {
		expectStartTime = startTime
	}
	if endTime, ok := eventData["expectGotEndDate"].(string); ok {
		expectEndTime = endTime
	}

	// 构建订单变更数据 - 使用状态变更事件，因为这不会改变订单状态
	statusData := &model.OrderStatusChangedData{
		NewStatus:  "", // 不改变状态，只是信息更新
		StatusDesc: "修改期望揽收时间",
		UpdateTime: util.NowBeijing(),
		Extra: map[string]interface{}{
			"event_type":        "MODIFY_EXPECT_GOT_TIME_SUCCESS",
			"expect_start_time": expectStartTime,
			"expect_end_time":   expectEndTime,
			"provider_data":     eventData,
		},
	}

	return &model.StandardizedCallbackData{
		EventType:       model.EventTypeOrderStatusChanged,
		OrderNo:         parsedData.OrderNo,
		CustomerOrderNo: parsedData.CustomerOrderNo,
		TrackingNo:      parsedData.TrackingNo,
		Provider:        model.CallbackProviderCainiao,
		Timestamp:       util.NowBeijing(),
		Data:            statusData,
	}, nil
}

// standardizeCainiaoCourierChange 标准化菜鸟改派快递员事件
func (s *CallbackStandardizer) standardizeCainiaoCourierChange(parsedData *model.ParsedCallbackData, dataMap map[string]interface{}, externalOrder map[string]interface{}, orderEvent map[string]interface{}) (*model.StandardizedCallbackData, error) {
	// 提取事件数据
	eventData, _ := orderEvent["eventData"].(map[string]interface{})

	// 提取快递员信息
	var courierName, courierMobile, courierCompany string
	if name, ok := eventData["courierName"].(string); ok {
		courierName = name
	}
	if mobile, ok := eventData["courierMobile"].(string); ok {
		courierMobile = mobile
	}
	if company, ok := eventData["courierCompany"].(string); ok {
		courierCompany = company
	}

	// 构建状态变更数据 - 快递员改派不改变订单状态，只更新快递员信息
	statusData := &model.OrderStatusChangedData{
		NewStatus:  "", // 不改变状态，只是信息更新
		StatusDesc: "改派成功",
		UpdateTime: util.NowBeijing(),
		Extra: map[string]interface{}{
			"event_type":      "CHANGE_DELIVERY_USER_SUCCESS",
			"courier_name":    courierName,
			"courier_mobile":  courierMobile,
			"courier_company": courierCompany,
			"provider_data":   eventData,
		},
	}

	return &model.StandardizedCallbackData{
		EventType:       model.EventTypeOrderStatusChanged,
		OrderNo:         parsedData.OrderNo,
		CustomerOrderNo: parsedData.CustomerOrderNo,
		TrackingNo:      parsedData.TrackingNo,
		Provider:        model.CallbackProviderCainiao,
		Timestamp:       util.NowBeijing(),
		Data:            statusData,
	}, nil
}

// standardizeCainiaoUploadMailNo 标准化菜鸟回单成功事件
func (s *CallbackStandardizer) standardizeCainiaoUploadMailNo(parsedData *model.ParsedCallbackData, dataMap map[string]interface{}, externalOrder map[string]interface{}, orderEvent map[string]interface{}) (*model.StandardizedCallbackData, error) {
	// 提取事件数据
	eventData, _ := orderEvent["eventData"].(map[string]interface{})

	// 提取运单信息
	var mailNo, logisticsCompanyCode, logisticsCompanyName string
	if trackingNo, ok := eventData["mailNo"].(string); ok {
		mailNo = trackingNo
	}
	if companyCode, ok := eventData["logisticsCompanyCode"].(string); ok {
		logisticsCompanyCode = companyCode
	}
	if companyName, ok := eventData["logisticsCompanyName"].(string); ok {
		logisticsCompanyName = companyName
	}

	// 构建状态变更数据 - 回单成功不改变订单状态，只更新运单信息
	statusData := &model.OrderStatusChangedData{
		NewStatus:  "", // 不改变状态，只是信息更新
		StatusDesc: "回单成功",
		UpdateTime: util.NowBeijing(),
		Extra: map[string]interface{}{
			"event_type":             "UPLOAD_MAIL_NO_SUCCESS",
			"tracking_no":            mailNo,
			"logistics_company_code": logisticsCompanyCode,
			"logistics_company_name": logisticsCompanyName,
			"provider_data":          eventData,
		},
	}

	return &model.StandardizedCallbackData{
		EventType:       model.EventTypeOrderStatusChanged,
		OrderNo:         parsedData.OrderNo,
		CustomerOrderNo: parsedData.CustomerOrderNo,
		TrackingNo:      mailNo, // 使用新的运单号
		Provider:        model.CallbackProviderCainiao,
		Timestamp:       util.NowBeijing(),
		Data:            statusData,
	}, nil
}

// standardizeKuaidiniao 标准化快递鸟回调数据
func (s *CallbackStandardizer) standardizeKuaidiniao(parsedData *model.ParsedCallbackData) (*model.StandardizedCallbackData, error) {
	// 根据回调类型分别处理（覆盖工单、赔付等新类型）
	switch parsedData.Type {
	case model.EventTypeOrderStatusChanged:
		return s.standardizeKuaidiniaoStatusUpdate(parsedData)
	case model.EventTypeBillingUpdated:
		return s.standardizeKuaidiNiaoBillingUpdate(parsedData)
	case model.EventTypeWorkOrderUpdated, "workorder_compensation", model.EventTypeWorkOrderCreated, model.EventTypeWorkOrderReplied, model.EventTypeWorkOrderClosed:
		return s.standardizeKuaidiniaoWorkOrder(parsedData)
	default:
		return nil, fmt.Errorf("未知的快递鸟回调类型: %s", parsedData.Type)
	}
}

// standardizeKuaidiniaoWorkOrder 标准化快递鸟工单/赔付类回调
func (s *CallbackStandardizer) standardizeKuaidiniaoWorkOrder(parsedData *model.ParsedCallbackData) (*model.StandardizedCallbackData, error) {
	rawData, ok := parsedData.Data.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("快递鸟工单数据格式错误")
	}

	// 公共字段
	orderNo := parsedData.OrderNo
	customerOrderNo := parsedData.CustomerOrderNo
	trackingNo := parsedData.TrackingNo
	eventTime := parsedData.Timestamp
	if pushTimeStr, _ := rawData["PushTime"].(string); pushTimeStr != "" {
		if t, err := s.parseKuaidiniaoTime(pushTimeStr); err == nil {
			eventTime = t
		}
	}

	switch parsedData.Type {
	case model.EventTypeWorkOrderUpdated:
		// 🔥 修复：严格按照原始回调数据提取status，不使用任何硬编码
		state := s.getStringFromMap(rawData, "State")
		status := s.getIntFromMap(rawData, "Status")

		// 🔥 如果没有Status字段，记录错误
		if status == 0 {
			s.logger.Error("快递鸟工单回调缺少Status字段",
				zap.String("state", state),
				zap.Any("raw_data", rawData))
			// 不使用硬编码，直接使用原始的0值
		}
		content := s.getStringFromMap(rawData, "DealResult")
		if content == "" {
			content = s.getStringFromMap(rawData, "Reason")
		}
		if content == "" {
			content = "工单已处理"
		}
		committer := s.getStringFromMap(rawData, "Committer")
		attachments := s.getStringSliceFromMap(rawData, "DealResultFiles")
		workType := s.getIntFromMap(rawData, "WorkOrderType")
		if workType == 0 {
			workType = s.getIntFromMap(rawData, "TicketSource")
		}
		if workType == 0 {
			workType = s.getIntFromMap(rawData, "OperateType")
		}
		workTypeName := s.getStringFromMap(rawData, "WorkOrderTypeName")
		if workTypeName == "" {
			switch workType {
			case 4:
				workTypeName = "平台工单"
			case 3:
				workTypeName = "破损/丢失/少件"
			case 2:
				workTypeName = "服务问题"
			case 1:
				workTypeName = "时效问题"
			default:
				workTypeName = "工单"
			}
		}
		// 工单ID
		workOrderID := s.getStringFromMap(rawData, "TicketId")
		providerWorkOrderID := s.getStringFromMap(rawData, "KDNOrderCode")
		if providerWorkOrderID == "" {
			providerWorkOrderID = s.getStringFromMap(rawData, "KdnOrderCode")
		}

		data := &model.WorkOrderCallbackData{
			WorkOrderID:         workOrderID,
			ProviderWorkOrderID: providerWorkOrderID,
			Status:              status,
			StatusName:          "工单处理结果",
			Content:             content,
			Committer:           committer,
			AttachmentURLs:      attachments,
			WorkOrderType:       workType,
			WorkOrderTypeName:   workTypeName,
			UpdatedAt:           eventTime,
		}

		return &model.StandardizedCallbackData{
			EventType:       model.EventTypeWorkOrderUpdated,
			OrderNo:         orderNo,
			CustomerOrderNo: customerOrderNo,
			TrackingNo:      trackingNo,
			Provider:        model.CallbackProviderKuaidiniao,
			Timestamp:       eventTime,
			Data:            data,
		}, nil

	case "workorder_compensation":
		amount := s.getFloat64FromMap(rawData, "PaymentTotalAmount")
		reason := s.getStringFromMap(rawData, "CompensationReason")
		paymentNo := s.getStringFromMap(rawData, "PaymentNumber")

		data := map[string]interface{}{
			"compensation_amount": amount,
			"compensation_reason": reason,
			"payment_no":          paymentNo,
		}

		return &model.StandardizedCallbackData{
			EventType:       model.EventTypeWorkOrderUpdated,
			OrderNo:         orderNo,
			CustomerOrderNo: customerOrderNo,
			TrackingNo:      trackingNo,
			Provider:        model.CallbackProviderKuaidiniao,
			Timestamp:       eventTime,
			Data:            data,
		}, nil
	}

	return nil, fmt.Errorf("未知的快递鸟工单事件类型: %s", parsedData.Type)
}

// standardizeKuaidiniaoStatusUpdate 标准化快递鸟状态更新回调
func (s *CallbackStandardizer) standardizeKuaidiniaoStatusUpdate(parsedData *model.ParsedCallbackData) (*model.StandardizedCallbackData, error) {
	rawData := parsedData.Data.(map[string]interface{})

	// 提取状态信息
	state := s.getStringFromMap(rawData, "State")
	stateEx := s.getStringFromMap(rawData, "StateEx")
	location := s.getStringFromMap(rawData, "Location")

	// 映射状态到系统标准状态
	standardStatus := s.mapKuaidiniaoState(state, stateEx)
	statusDesc := s.getKuaidiniaoStateDescription(state, stateEx)

	// 提取时间
	eventTime := parsedData.Timestamp
	if pushTimeStr := s.getStringFromMap(rawData, "PushTime"); pushTimeStr != "" {
		if t, err := s.parseKuaidiniaoTime(pushTimeStr); err == nil {
			eventTime = t
		}
	}

	// 🔥 修复：提取快递员信息（PickerInfo）
	var courierInfo *model.CourierInfo
	if pickerInfoArray, ok := rawData["PickerInfo"].([]interface{}); ok && len(pickerInfoArray) > 0 {
		if pickerInfo, ok := pickerInfoArray[0].(map[string]interface{}); ok {
			// 提取快递员信息
			if personName, ok := pickerInfo["PersonName"].(string); ok && personName != "" {
				courierInfo = &model.CourierInfo{
					Name: personName,
				}

				// 提取快递员电话
				if personTel, ok := pickerInfo["PersonTel"].(string); ok && personTel != "" {
					courierInfo.Phone = personTel
				}

				// 提取取件码
				if pickupCode, ok := pickerInfo["PickupCode"].(string); ok && pickupCode != "" {
					courierInfo.PickupCode = pickupCode
				}

				// 提取快递员编号
				if personCode, ok := pickerInfo["PersonCode"].(string); ok && personCode != "" {
					courierInfo.Code = personCode
				}
			}
		}
	}

	// 构建状态变更数据
	statusData := &model.OrderStatusChangedData{
		NewStatus:   standardStatus,
		StatusDesc:  statusDesc,
		UpdateTime:  eventTime,
		CourierInfo: courierInfo, // 🔥 修复：添加快递员信息
		Extra: map[string]interface{}{
			"state":    state,
			"state_ex": stateEx,
			"location": location,
			"provider": "kuaidiniao",
		},
	}

	// 如果有轨迹信息，添加到Extra中
	if traces, ok := rawData["Traces"].([]interface{}); ok && len(traces) > 0 {
		statusData.Extra["traces"] = traces
	}

	return &model.StandardizedCallbackData{
		EventType:       model.EventTypeOrderStatusChanged,
		OrderNo:         parsedData.OrderNo,
		CustomerOrderNo: parsedData.CustomerOrderNo,
		TrackingNo:      parsedData.TrackingNo,
		Provider:        model.CallbackProviderKuaidiniao,
		Timestamp:       eventTime,
		Data:            statusData,
	}, nil
}

// standardizeKuaidiNiaoBillingUpdate 标准化快递鸟计费更新回调
func (s *CallbackStandardizer) standardizeKuaidiNiaoBillingUpdate(parsedData *model.ParsedCallbackData) (*model.StandardizedCallbackData, error) {
	rawData, ok := parsedData.Data.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("快递鸟计费数据格式错误: 期望map[string]interface{}, 实际: %T", parsedData.Data)
	}

	// 本地安全转换函数（支持 string/float/int 等）
	convert := func(v interface{}) float64 {
		switch val := v.(type) {
		case nil:
			return 0
		case float64:
			return val
		case float32:
			return float64(val)
		case int:
			return float64(val)
		case int64:
			return float64(val)
		case string:
			if val == "" {
				return 0
			}
			if f, err := strconv.ParseFloat(val, 64); err == nil {
				return f
			}
			return 0
		default:
			return 0
		}
	}

	// 提取基础字段
	weight := convert(rawData["Weight"])     // 订单重量
	cost := convert(rawData["Cost"])         // 基础费用
	totalFee := convert(rawData["TotalFee"]) // 总费用（有些快递公司推这个）
	volume := convert(rawData["Volume"])     // 体积

	// 选择最终总费用
	finalTotalFee := totalFee
	if finalTotalFee <= 0 {
		finalTotalFee = cost
	}

	// 业务要求：快递鸟计费回调统一使用 Weight 作为计费重量
	// 直接取 Weight，避免使用 ActualWeight 导致与实际计费不一致
	chargedWeight := weight
	if chargedWeight <= 0 {
		// 兜底：若 Weight 异常，尝试使用 (FirstWeight + ContinuousWeight)，再退到 VolumeWeight
		fw := convert(rawData["FirstWeight"])
		cw := convert(rawData["ContinuousWeight"])
		if fw+cw > 0 {
			chargedWeight = fw + cw
		} else {
			if vw := convert(rawData["VolumeWeight"]); vw > 0 {
				chargedWeight = vw
			}
		}
	}

	// 提取时间
	eventTime := parsedData.Timestamp
	if pushTimeStr := s.getStringFromMap(rawData, "PushTime"); pushTimeStr != "" {
		if t, err := s.parseKuaidiniaoTime(pushTimeStr); err == nil {
			eventTime = t
		}
	}

	// 构建计费数据（含计费重量）
	billingData := &model.BillingUpdatedData{
		Weight:        weight,
		ChargedWeight: chargedWeight,
		Volume:        volume,
		Cost:          cost,
		TotalFee:      finalTotalFee,
		UpdateTime:    eventTime,
		FeeDetails: []model.FeeDetail{
			{
				FeeType: "freight",
				FeeDesc: "运费",
				Amount:  finalTotalFee,
			},
		},
	}

	// 调试日志（仅在有logger时输出）
	if s.logger != nil {
		s.logger.Info("KDN 计费标准化",
			zap.String("order_no", parsedData.OrderNo),
			zap.String("tracking_no", parsedData.TrackingNo),
			zap.Float64("weight", weight),
			zap.Float64("charged_weight", chargedWeight),
			zap.Float64("cost", cost),
			zap.Float64("total_fee", finalTotalFee))
		if chargedWeight <= 0 {
			s.logger.Warn("KDN 计费重量为0，请检查字段映射", zap.Any("raw_keys", rawData))
		}
	}

	return &model.StandardizedCallbackData{
		EventType:       model.EventTypeBillingUpdated,
		OrderNo:         parsedData.OrderNo,
		CustomerOrderNo: parsedData.CustomerOrderNo,
		TrackingNo:      parsedData.TrackingNo,
		Provider:        model.CallbackProviderKuaidiniao,
		Timestamp:       eventTime,
		Data:            billingData,
	}, nil
}

// mapKuaidiniaoState 映射快递鸟状态到系统标准状态
func (s *CallbackStandardizer) mapKuaidiniaoState(state, stateEx string) string {
	// 优先使用详细状态StateEx
	if stateEx != "" {
		switch stateEx {
		case "1":
			return model.OrderStatusPickedUp // 已揽收
		case "2":
			return model.OrderStatusInTransit // 在途中
		case "3":
			return model.OrderStatusDelivered // 已签收
		case "201":
			return model.OrderStatusInTransit // 到达派件城市
		case "202":
			return model.OrderStatusOutForDelivery // 派件中
		case "204":
			return model.OrderStatusInTransit // 到达中转站
		case "301":
			return model.OrderStatusException // 异常
		case "302":
			return model.OrderStatusDelivered // 正常签收
		case "304":
			return model.OrderStatusDeliveredAbnormal // 拒收
		case "311":
			return model.OrderStatusException // 丢失
		case "412":
			return model.OrderStatusInTransit // 代收点暂存
		}
	}

	// 使用基础状态State
	switch state {
	case "1":
		return model.OrderStatusPickedUp // 已揽收
	case "2":
		return model.OrderStatusInTransit // 在途中
	case "3":
		return model.OrderStatusDelivered // 已签收
	case "99":
		return model.OrderStatusCancelled // 🔥 优化：调度失败直接映射为已取消
	case "102":
		return model.OrderStatusAssigned // 已分配快递员/网点
	case "103":
		return model.OrderStatusAssigned // 已分配快递员
	case "104":
		return model.OrderStatusPickedUp // 已取件
	case "109":
		return model.OrderStatusAssigned // 推送调派通知
	case "110":
		return model.OrderStatusAssigned // 推送预约时间变更
	case "203":
		return model.OrderStatusCancelled // 订单已取消
	case "206":
		return model.OrderStatusException // 推送虚假揽件状态
	case "207":
		return model.OrderStatusBilled // 推送线下收费状态
	case "208":
		return model.OrderStatusWeightUpdated // 推送重量修正结果
	case "209":
		return model.OrderStatusCancelled // 揽件未支付取消
	case "301":
		return model.OrderStatusPickedUp // 推送揽件状态
	case "302":
		return model.OrderStatusInTransit // 推送更换运单号
	case "401":
		return model.OrderStatusException // 推送工单处理结果
	case "402":
		return model.OrderStatusAssigned // 推送修改订单结果
	case "501":
		return model.OrderStatusException // 推送工单赔付结果
	case "502":
		return model.OrderStatusInTransit // 推送子母件信息
	case "601":
		return model.OrderStatusBilled // 推送订单费用状态
	case "701":
		return model.OrderStatusBilled // 获取平台支付结果
	default:
		return model.OrderStatusSubmitted // 默认状态
	}
}

// getKuaidiniaoStateDescription 获取快递鸟状态描述
func (s *CallbackStandardizer) getKuaidiniaoStateDescription(state, stateEx string) string {
	// 优先使用详细状态描述
	if stateEx != "" {
		switch stateEx {
		case "1":
			return "已揽收"
		case "2":
			return "在途中"
		case "3":
			return "已签收"
		case "201":
			return "到达派件城市"
		case "202":
			return "派件中"
		case "204":
			return "到达中转站"
		case "301":
			return "异常"
		case "302":
			return "正常签收"
		case "304":
			return "拒收"
		case "311":
			return "丢失"
		case "412":
			return "代收点暂存"
		default:
			return fmt.Sprintf("状态码: %s", stateEx)
		}
	}

	// 使用基础状态描述
	switch state {
	case "1":
		return "已揽收"
	case "2":
		return "在途中"
	case "3":
		return "已签收"
	case "99":
		return "订单已取消" // 🔥 优化：调度失败描述为订单已取消
	case "102":
		return "已分配网点"
	case "103":
		return "已分配快递员"
	case "104":
		return "已取件"
	case "109":
		return "推送调派通知"
	case "110":
		return "预约时间变更"
	case "203":
		return "订单已取消"
	case "206":
		return "虚假揽件状态"
	case "207":
		return "线下收费状态"
	case "208":
		return "重量修正结果"
	case "209":
		return "揽件未支付取消"
	case "301":
		return "推送揽件状态"
	case "302":
		return "更换运单号"
	case "401":
		return "工单处理结果"
	case "402":
		return "修改订单结果"
	case "501":
		return "工单赔付结果"
	case "502":
		return "子母件信息"
	case "601":
		return "订单费用状态"
	case "701":
		return "平台支付结果"
	default:
		return fmt.Sprintf("状态码: %s", state)
	}
}

// parseKuaidiniaoTime 解析快递鸟时间格式
func (s *CallbackStandardizer) parseKuaidiniaoTime(timeStr string) (time.Time, error) {
	// 快递鸟时间格式：2025-07-20 10:55:00
	layouts := []string{
		"2006-01-02 15:04:05",
		"2006-01-02T15:04:05",
		"2006-01-02T15:04:05Z",
		"2006-01-02T15:04:05+08:00",
	}

	for _, layout := range layouts {
		if t, err := time.Parse(layout, timeStr); err == nil {
			// 确保时间是北京时间
			return util.ToBeijing(t), nil
		}
	}

	return time.Time{}, fmt.Errorf("无法解析时间格式: %s", timeStr)
}
