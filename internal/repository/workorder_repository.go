package repository

import (
	"context"
	"database/sql"
	"fmt"
	"strings"

	"github.com/google/uuid"
	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/util"
	"go.uber.org/zap"
)

// WorkOrderRepository 工单仓储接口
type WorkOrderRepository interface {
	// 工单管理
	Create(ctx context.Context, workOrder *model.WorkOrder) error
	GetByID(ctx context.Context, id uuid.UUID) (*model.WorkOrder, error)
	GetByProviderWorkOrderID(ctx context.Context, provider, providerWorkOrderID string) (*model.WorkOrder, error)
	Update(ctx context.Context, workOrder *model.WorkOrder) error
	Delete(ctx context.Context, id uuid.UUID) error
	List(ctx context.Context, userID string, req *model.WorkOrderListRequest) (*model.WorkOrderListResponse, error)

	// 🔥 新增：用户自定义工单ID管理
	ExistsByCustomerWorkOrderID(ctx context.Context, userID, customerWorkOrderID string) (bool, error)

	// 工单回复管理
	CreateReply(ctx context.Context, reply *model.WorkOrderReply) error
	GetRepliesByWorkOrderID(ctx context.Context, workOrderID uuid.UUID) ([]model.WorkOrderReply, error)

	// 附件管理
	CreateAttachment(ctx context.Context, attachment *model.WorkOrderAttachment) error
	GetAttachmentsByWorkOrderID(ctx context.Context, workOrderID uuid.UUID) ([]model.WorkOrderAttachment, error)
	GetAttachmentsByReplyID(ctx context.Context, replyID uuid.UUID) ([]model.WorkOrderAttachment, error)
	UpdateAttachmentWorkOrderID(ctx context.Context, attachmentIDs []uuid.UUID, workOrderID uuid.UUID) error

	// 映射管理
	GetTypeMapping(ctx context.Context, unifiedType int, provider string) (*model.WorkOrderTypeMapping, error)
	GetStatusMapping(ctx context.Context, unifiedStatus int, provider string) (*model.WorkOrderStatusMapping, error)
	GetProviderTypeMapping(ctx context.Context, providerType int, provider string) (*model.WorkOrderTypeMapping, error)
	GetProviderStatusMapping(ctx context.Context, providerStatus int, provider string) (*model.WorkOrderStatusMapping, error)
	ListSupportedTypes(ctx context.Context, provider string) ([]model.WorkOrderTypeMapping, error)
	ListAllSupportedTypes(ctx context.Context) ([]model.WorkOrderTypeMapping, error)
}

// PostgresWorkOrderRepository PostgreSQL工单仓储实现
type PostgresWorkOrderRepository struct {
	db     *sql.DB
	logger *zap.Logger
}

// NewPostgresWorkOrderRepository 创建PostgreSQL工单仓储
func NewPostgresWorkOrderRepository(db *sql.DB, logger *zap.Logger) WorkOrderRepository {
	return &PostgresWorkOrderRepository{
		db:     db,
		logger: logger,
	}
}

// Create 创建工单
func (r *PostgresWorkOrderRepository) Create(ctx context.Context, workOrder *model.WorkOrder) error {
	query := `
		INSERT INTO work_orders (
			id, user_id, customer_work_order_id, order_no, tracking_no, provider, provider_work_order_id,
			work_order_type, title, content, status, priority, version, feedback_weight,
			goods_value, overweight_amount, callback_url, message_callback_url,
			raw_request_data, provider_response_data, created_at, updated_at
		) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22)
	`

	// 生成UUID
	if workOrder.ID == uuid.Nil {
		workOrder.ID = uuid.New()
	}

	// 设置时间戳
	now := util.NowBeijing()
	workOrder.CreatedAt = now
	workOrder.UpdatedAt = now

	// 初始化版本号为1
	if workOrder.Version == 0 {
		workOrder.Version = 1
	}

	_, err := r.db.ExecContext(ctx, query,
		workOrder.ID,
		workOrder.UserID,
		workOrder.CustomerWorkOrderID,
		workOrder.OrderNo,
		workOrder.TrackingNo,
		workOrder.Provider,
		workOrder.ProviderWorkOrderID,
		workOrder.WorkOrderType,
		workOrder.Title,
		workOrder.Content,
		workOrder.Status,
		workOrder.Priority,
		workOrder.Version,
		workOrder.FeedbackWeight,
		workOrder.GoodsValue,
		workOrder.OverweightAmount,
		workOrder.CallbackURL,
		workOrder.MessageCallbackURL,
		workOrder.RawRequestData,
		workOrder.ProviderResponseData,
		workOrder.CreatedAt,
		workOrder.UpdatedAt,
	)

	if err != nil {
		r.logger.Error("创建工单失败", zap.Error(err), zap.String("work_order_id", workOrder.ID.String()))
		return fmt.Errorf("创建工单失败: %w", err)
	}

	return nil
}

// GetByID 根据ID获取工单
func (r *PostgresWorkOrderRepository) GetByID(ctx context.Context, id uuid.UUID) (*model.WorkOrder, error) {
	query := `
		SELECT
			id, user_id, order_no, tracking_no, provider, provider_work_order_id,
			work_order_type, title, content, status, priority, version, feedback_weight,
			goods_value, overweight_amount, callback_url, message_callback_url,
			raw_request_data, provider_response_data, created_at, updated_at, completed_at, deleted_at
		FROM work_orders
		WHERE id = $1 AND deleted_at IS NULL
	`

	var workOrder model.WorkOrder
	err := r.db.QueryRowContext(ctx, query, id).Scan(
		&workOrder.ID,
		&workOrder.UserID,
		&workOrder.OrderNo,
		&workOrder.TrackingNo,
		&workOrder.Provider,
		&workOrder.ProviderWorkOrderID,
		&workOrder.WorkOrderType,
		&workOrder.Title,
		&workOrder.Content,
		&workOrder.Status,
		&workOrder.Priority,
		&workOrder.Version,
		&workOrder.FeedbackWeight,
		&workOrder.GoodsValue,
		&workOrder.OverweightAmount,
		&workOrder.CallbackURL,
		&workOrder.MessageCallbackURL,
		&workOrder.RawRequestData,
		&workOrder.ProviderResponseData,
		&workOrder.CreatedAt,
		&workOrder.UpdatedAt,
		&workOrder.CompletedAt,
		&workOrder.DeletedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("工单不存在: %s", id.String())
		}
		r.logger.Error("获取工单失败", zap.Error(err), zap.String("work_order_id", id.String()))
		return nil, fmt.Errorf("获取工单失败: %w", err)
	}

	return &workOrder, nil
}

// GetByProviderWorkOrderID 根据供应商工单ID获取工单
func (r *PostgresWorkOrderRepository) GetByProviderWorkOrderID(ctx context.Context, provider, providerWorkOrderID string) (*model.WorkOrder, error) {
	query := `
		SELECT 
			id, user_id, order_no, tracking_no, provider, provider_work_order_id,
			work_order_type, title, content, status, priority, feedback_weight,
			goods_value, overweight_amount, callback_url, message_callback_url,
			raw_request_data, provider_response_data, created_at, updated_at, completed_at
		FROM work_orders 
		WHERE provider = $1 AND provider_work_order_id = $2
	`

	var workOrder model.WorkOrder
	err := r.db.QueryRowContext(ctx, query, provider, providerWorkOrderID).Scan(
		&workOrder.ID,
		&workOrder.UserID,
		&workOrder.OrderNo,
		&workOrder.TrackingNo,
		&workOrder.Provider,
		&workOrder.ProviderWorkOrderID,
		&workOrder.WorkOrderType,
		&workOrder.Title,
		&workOrder.Content,
		&workOrder.Status,
		&workOrder.Priority,
		&workOrder.FeedbackWeight,
		&workOrder.GoodsValue,
		&workOrder.OverweightAmount,
		&workOrder.CallbackURL,
		&workOrder.MessageCallbackURL,
		&workOrder.RawRequestData,
		&workOrder.ProviderResponseData,
		&workOrder.CreatedAt,
		&workOrder.UpdatedAt,
		&workOrder.CompletedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("工单不存在: provider=%s, provider_work_order_id=%s", provider, providerWorkOrderID)
		}
		r.logger.Error("获取工单失败", zap.Error(err), zap.String("provider", provider), zap.String("provider_work_order_id", providerWorkOrderID))
		return nil, fmt.Errorf("获取工单失败: %w", err)
	}

	return &workOrder, nil
}

// Update 更新工单
func (r *PostgresWorkOrderRepository) Update(ctx context.Context, workOrder *model.WorkOrder) error {
	query := `
		UPDATE work_orders SET
			provider_work_order_id = $2,
			status = $3,
			priority = $4,
			feedback_weight = $5,
			goods_value = $6,
			overweight_amount = $7,
			callback_url = $8,
			message_callback_url = $9,
			provider_response_data = $10,
			updated_at = $11,
			completed_at = $12
		WHERE id = $1
	`

	workOrder.UpdatedAt = util.NowBeijing()

	_, err := r.db.ExecContext(ctx, query,
		workOrder.ID,
		workOrder.ProviderWorkOrderID,
		workOrder.Status,
		workOrder.Priority,
		workOrder.FeedbackWeight,
		workOrder.GoodsValue,
		workOrder.OverweightAmount,
		workOrder.CallbackURL,
		workOrder.MessageCallbackURL,
		workOrder.ProviderResponseData,
		workOrder.UpdatedAt,
		workOrder.CompletedAt,
	)

	if err != nil {
		r.logger.Error("更新工单失败", zap.Error(err), zap.String("work_order_id", workOrder.ID.String()))
		return fmt.Errorf("更新工单失败: %w", err)
	}

	return nil
}

// Delete 删除工单
func (r *PostgresWorkOrderRepository) Delete(ctx context.Context, id uuid.UUID) error {
	query := `DELETE FROM work_orders WHERE id = $1`

	result, err := r.db.ExecContext(ctx, query, id)
	if err != nil {
		r.logger.Error("删除工单失败", zap.Error(err), zap.String("work_order_id", id.String()))
		return fmt.Errorf("删除工单失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		r.logger.Error("获取删除工单影响行数失败", zap.Error(err))
		return fmt.Errorf("获取删除工单影响行数失败: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("工单不存在: %s", id.String())
	}

	r.logger.Info("工单删除成功", zap.String("work_order_id", id.String()))
	return nil
}

// UpdateWithOptimisticLock 使用乐观锁更新工单
func (r *PostgresWorkOrderRepository) UpdateWithOptimisticLock(ctx context.Context, workOrder *model.WorkOrder) error {
	query := `
		UPDATE work_orders
		SET provider_work_order_id = $2, status = $3, priority = $4,
		    feedback_weight = $5, goods_value = $6, overweight_amount = $7,
		    callback_url = $8, message_callback_url = $9,
		    provider_response_data = $10, version = version + 1,
		    updated_at = NOW(), completed_at = $11
		WHERE id = $1 AND version = $12
	`

	result, err := r.db.ExecContext(ctx, query,
		workOrder.ID,
		workOrder.ProviderWorkOrderID,
		workOrder.Status,
		workOrder.Priority,
		workOrder.FeedbackWeight,
		workOrder.GoodsValue,
		workOrder.OverweightAmount,
		workOrder.CallbackURL,
		workOrder.MessageCallbackURL,
		workOrder.ProviderResponseData,
		workOrder.CompletedAt,
		workOrder.Version,
	)

	if err != nil {
		r.logger.Error("乐观锁更新工单失败", zap.Error(err), zap.String("work_order_id", workOrder.ID.String()))
		return fmt.Errorf("乐观锁更新工单失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取影响行数失败: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("工单已被其他操作修改，请刷新后重试")
	}

	// 更新内存中的版本号和时间
	workOrder.Version++
	workOrder.UpdatedAt = util.NowBeijing()

	return nil
}

// CreateReply 创建工单回复
func (r *PostgresWorkOrderRepository) CreateReply(ctx context.Context, reply *model.WorkOrderReply) error {
	query := `
		INSERT INTO work_order_replies (
			id, work_order_id, reply_type, committer, content, raw_data, created_at
		) VALUES ($1, $2, $3, $4, $5, $6, $7)
	`

	// 生成UUID
	if reply.ID == uuid.Nil {
		reply.ID = uuid.New()
	}

	// 设置时间戳
	reply.CreatedAt = util.NowBeijing()

	_, err := r.db.ExecContext(ctx, query,
		reply.ID,
		reply.WorkOrderID,
		reply.ReplyType,
		reply.Committer,
		reply.Content,
		reply.RawData,
		reply.CreatedAt,
	)

	if err != nil {
		r.logger.Error("创建工单回复失败", zap.Error(err), zap.String("reply_id", reply.ID.String()))
		return fmt.Errorf("创建工单回复失败: %w", err)
	}

	return nil
}

// CreateAttachment 创建附件
func (r *PostgresWorkOrderRepository) CreateAttachment(ctx context.Context, attachment *model.WorkOrderAttachment) error {
	query := `
		INSERT INTO work_order_attachments (
			id, work_order_id, reply_id, file_name, file_url, file_type, file_size, upload_type, created_at
		) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
	`

	// 生成UUID
	if attachment.ID == uuid.Nil {
		attachment.ID = uuid.New()
	}

	// 设置时间戳
	attachment.CreatedAt = util.NowBeijing()

	_, err := r.db.ExecContext(ctx, query,
		attachment.ID,
		attachment.WorkOrderID,
		attachment.ReplyID,
		attachment.FileName,
		attachment.FileURL,
		attachment.FileType,
		attachment.FileSize,
		attachment.UploadType,
		attachment.CreatedAt,
	)

	if err != nil {
		r.logger.Error("创建附件失败", zap.Error(err), zap.String("attachment_id", attachment.ID.String()))
		return fmt.Errorf("创建附件失败: %w", err)
	}

	return nil
}

// GetRepliesByWorkOrderID 获取工单回复列表
func (r *PostgresWorkOrderRepository) GetRepliesByWorkOrderID(ctx context.Context, workOrderID uuid.UUID) ([]model.WorkOrderReply, error) {
	query := `
		SELECT id, work_order_id, reply_type, committer, content, raw_data, created_at
		FROM work_order_replies
		WHERE work_order_id = $1
		ORDER BY created_at ASC
	`

	rows, err := r.db.QueryContext(ctx, query, workOrderID)
	if err != nil {
		r.logger.Error("获取工单回复失败", zap.Error(err), zap.String("work_order_id", workOrderID.String()))
		return nil, fmt.Errorf("获取工单回复失败: %w", err)
	}
	defer rows.Close()

	var replies []model.WorkOrderReply
	for rows.Next() {
		var reply model.WorkOrderReply
		err := rows.Scan(
			&reply.ID,
			&reply.WorkOrderID,
			&reply.ReplyType,
			&reply.Committer,
			&reply.Content,
			&reply.RawData,
			&reply.CreatedAt,
		)
		if err != nil {
			r.logger.Error("扫描工单回复失败", zap.Error(err))
			return nil, fmt.Errorf("扫描工单回复失败: %w", err)
		}
		replies = append(replies, reply)
	}

	return replies, nil
}

// GetAttachmentsByWorkOrderID 获取工单附件列表
func (r *PostgresWorkOrderRepository) GetAttachmentsByWorkOrderID(ctx context.Context, workOrderID uuid.UUID) ([]model.WorkOrderAttachment, error) {
	query := `
		SELECT id, work_order_id, reply_id, file_name, file_url, file_type, file_size, upload_type, created_at
		FROM work_order_attachments
		WHERE work_order_id = $1
		ORDER BY created_at ASC
	`

	rows, err := r.db.QueryContext(ctx, query, workOrderID)
	if err != nil {
		r.logger.Error("获取工单附件失败", zap.Error(err), zap.String("work_order_id", workOrderID.String()))
		return nil, fmt.Errorf("获取工单附件失败: %w", err)
	}
	defer rows.Close()

	var attachments []model.WorkOrderAttachment
	for rows.Next() {
		var attachment model.WorkOrderAttachment
		err := rows.Scan(
			&attachment.ID,
			&attachment.WorkOrderID,
			&attachment.ReplyID,
			&attachment.FileName,
			&attachment.FileURL,
			&attachment.FileType,
			&attachment.FileSize,
			&attachment.UploadType,
			&attachment.CreatedAt,
		)
		if err != nil {
			r.logger.Error("扫描工单附件失败", zap.Error(err))
			return nil, fmt.Errorf("扫描工单附件失败: %w", err)
		}
		attachments = append(attachments, attachment)
	}

	return attachments, nil
}

// GetAttachmentsByReplyID 获取回复附件列表
func (r *PostgresWorkOrderRepository) GetAttachmentsByReplyID(ctx context.Context, replyID uuid.UUID) ([]model.WorkOrderAttachment, error) {
	query := `
		SELECT id, work_order_id, reply_id, file_name, file_url, file_type, file_size, upload_type, created_at
		FROM work_order_attachments
		WHERE reply_id = $1
		ORDER BY created_at ASC
	`

	rows, err := r.db.QueryContext(ctx, query, replyID)
	if err != nil {
		r.logger.Error("获取回复附件失败", zap.Error(err), zap.String("reply_id", replyID.String()))
		return nil, fmt.Errorf("获取回复附件失败: %w", err)
	}
	defer rows.Close()

	var attachments []model.WorkOrderAttachment
	for rows.Next() {
		var attachment model.WorkOrderAttachment
		err := rows.Scan(
			&attachment.ID,
			&attachment.WorkOrderID,
			&attachment.ReplyID,
			&attachment.FileName,
			&attachment.FileURL,
			&attachment.FileType,
			&attachment.FileSize,
			&attachment.UploadType,
			&attachment.CreatedAt,
		)
		if err != nil {
			r.logger.Error("扫描回复附件失败", zap.Error(err))
			return nil, fmt.Errorf("扫描回复附件失败: %w", err)
		}
		attachments = append(attachments, attachment)
	}

	return attachments, nil
}

// UpdateAttachmentWorkOrderID 更新附件的工单ID
func (r *PostgresWorkOrderRepository) UpdateAttachmentWorkOrderID(ctx context.Context, attachmentIDs []uuid.UUID, workOrderID uuid.UUID) error {
	if len(attachmentIDs) == 0 {
		return nil
	}

	// 构建IN子句的占位符
	placeholders := make([]string, len(attachmentIDs))
	args := make([]interface{}, len(attachmentIDs)+1)
	args[0] = workOrderID

	for i, id := range attachmentIDs {
		placeholders[i] = fmt.Sprintf("$%d", i+2)
		args[i+1] = id
	}

	query := fmt.Sprintf(`
		UPDATE work_order_attachments
		SET work_order_id = $1
		WHERE id IN (%s)
	`, strings.Join(placeholders, ","))

	_, err := r.db.ExecContext(ctx, query, args...)
	if err != nil {
		r.logger.Error("更新附件工单ID失败", zap.Error(err), zap.String("work_order_id", workOrderID.String()))
		return fmt.Errorf("更新附件工单ID失败: %w", err)
	}

	return nil
}

// List 获取工单列表
func (r *PostgresWorkOrderRepository) List(ctx context.Context, userID string, req *model.WorkOrderListRequest) (*model.WorkOrderListResponse, error) {
	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}
	if req.PageSize > 100 {
		req.PageSize = 100
	}

	// 构建WHERE条件
	var conditions []string
	var args []interface{}
	argIndex := 1

	// 用户ID条件
	conditions = append(conditions, fmt.Sprintf("user_id = $%d", argIndex))
	args = append(args, userID)
	argIndex++

	// 供应商条件
	if req.Provider != nil && *req.Provider != "" {
		conditions = append(conditions, fmt.Sprintf("provider = $%d", argIndex))
		args = append(args, *req.Provider)
		argIndex++
	}

	// 状态条件
	if req.Status != nil {
		conditions = append(conditions, fmt.Sprintf("status = $%d", argIndex))
		args = append(args, *req.Status)
		argIndex++
	}

	// 工单类型条件
	if req.WorkOrderType != nil {
		conditions = append(conditions, fmt.Sprintf("work_order_type = $%d", argIndex))
		args = append(args, *req.WorkOrderType)
		argIndex++
	}

	// 订单号条件
	if req.OrderNo != nil && *req.OrderNo != "" {
		conditions = append(conditions, fmt.Sprintf("order_no = $%d", argIndex))
		args = append(args, *req.OrderNo)
		argIndex++
	}

	// 运单号条件
	if req.TrackingNo != nil && *req.TrackingNo != "" {
		conditions = append(conditions, fmt.Sprintf("tracking_no = $%d", argIndex))
		args = append(args, *req.TrackingNo)
		argIndex++
	}

	// 时间范围条件
	if req.StartDate != nil && *req.StartDate != "" {
		conditions = append(conditions, fmt.Sprintf("created_at >= $%d", argIndex))
		args = append(args, *req.StartDate)
		argIndex++
	}

	if req.EndDate != nil && *req.EndDate != "" {
		conditions = append(conditions, fmt.Sprintf("created_at <= $%d", argIndex))
		args = append(args, *req.EndDate)
		argIndex++
	}

	whereClause := strings.Join(conditions, " AND ")

	// 查询总数
	countQuery := fmt.Sprintf("SELECT COUNT(*) FROM work_orders WHERE %s", whereClause)
	var total int64
	err := r.db.QueryRowContext(ctx, countQuery, args...).Scan(&total)
	if err != nil {
		r.logger.Error("查询工单总数失败", zap.Error(err))
		return nil, fmt.Errorf("查询工单总数失败: %w", err)
	}

	// 查询数据
	offset := (req.Page - 1) * req.PageSize
	dataQuery := fmt.Sprintf(`
		SELECT
			id, user_id, order_no, tracking_no, provider, provider_work_order_id,
			work_order_type, title, content, status, priority, feedback_weight,
			goods_value, overweight_amount, callback_url, message_callback_url,
			raw_request_data, provider_response_data, created_at, updated_at, completed_at
		FROM work_orders
		WHERE %s
		ORDER BY created_at DESC
		LIMIT $%d OFFSET $%d
	`, whereClause, argIndex, argIndex+1)

	args = append(args, req.PageSize, offset)

	rows, err := r.db.QueryContext(ctx, dataQuery, args...)
	if err != nil {
		r.logger.Error("查询工单列表失败", zap.Error(err))
		return nil, fmt.Errorf("查询工单列表失败: %w", err)
	}
	defer rows.Close()

	var workOrders []model.WorkOrder
	for rows.Next() {
		var workOrder model.WorkOrder
		err := rows.Scan(
			&workOrder.ID,
			&workOrder.UserID,
			&workOrder.OrderNo,
			&workOrder.TrackingNo,
			&workOrder.Provider,
			&workOrder.ProviderWorkOrderID,
			&workOrder.WorkOrderType,
			&workOrder.Title,
			&workOrder.Content,
			&workOrder.Status,
			&workOrder.Priority,
			&workOrder.FeedbackWeight,
			&workOrder.GoodsValue,
			&workOrder.OverweightAmount,
			&workOrder.CallbackURL,
			&workOrder.MessageCallbackURL,
			&workOrder.RawRequestData,
			&workOrder.ProviderResponseData,
			&workOrder.CreatedAt,
			&workOrder.UpdatedAt,
			&workOrder.CompletedAt,
		)
		if err != nil {
			r.logger.Error("扫描工单数据失败", zap.Error(err))
			return nil, fmt.Errorf("扫描工单数据失败: %w", err)
		}
		workOrders = append(workOrders, workOrder)
	}

	// 计算总页数
	totalPages := int((total + int64(req.PageSize) - 1) / int64(req.PageSize))

	return &model.WorkOrderListResponse{
		Items:      workOrders,
		Total:      total,
		Page:       req.Page,
		PageSize:   req.PageSize,
		TotalPages: totalPages,
	}, nil
}

// GetTypeMapping 获取工单类型映射
func (r *PostgresWorkOrderRepository) GetTypeMapping(ctx context.Context, unifiedType int, provider string) (*model.WorkOrderTypeMapping, error) {
	query := `
		SELECT id, unified_type, unified_name, provider, provider_type, provider_name, is_supported, description, created_at, updated_at
		FROM work_order_type_mappings
		WHERE unified_type = $1 AND provider = $2
	`

	var mapping model.WorkOrderTypeMapping
	err := r.db.QueryRowContext(ctx, query, unifiedType, provider).Scan(
		&mapping.ID,
		&mapping.UnifiedType,
		&mapping.UnifiedName,
		&mapping.Provider,
		&mapping.ProviderType,
		&mapping.ProviderName,
		&mapping.IsSupported,
		&mapping.Description,
		&mapping.CreatedAt,
		&mapping.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("工单类型映射不存在: unified_type=%d, provider=%s", unifiedType, provider)
		}
		r.logger.Error("获取工单类型映射失败", zap.Error(err))
		return nil, fmt.Errorf("获取工单类型映射失败: %w", err)
	}

	return &mapping, nil
}

// GetStatusMapping 获取工单状态映射
func (r *PostgresWorkOrderRepository) GetStatusMapping(ctx context.Context, unifiedStatus int, provider string) (*model.WorkOrderStatusMapping, error) {
	query := `
		SELECT id, unified_status, unified_name, provider, provider_status, provider_name, description, created_at, updated_at
		FROM work_order_status_mappings
		WHERE unified_status = $1 AND provider = $2
	`

	var mapping model.WorkOrderStatusMapping
	err := r.db.QueryRowContext(ctx, query, unifiedStatus, provider).Scan(
		&mapping.ID,
		&mapping.UnifiedStatus,
		&mapping.UnifiedName,
		&mapping.Provider,
		&mapping.ProviderStatus,
		&mapping.ProviderName,
		&mapping.Description,
		&mapping.CreatedAt,
		&mapping.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("工单状态映射不存在: unified_status=%d, provider=%s", unifiedStatus, provider)
		}
		r.logger.Error("获取工单状态映射失败", zap.Error(err))
		return nil, fmt.Errorf("获取工单状态映射失败: %w", err)
	}

	return &mapping, nil
}

// GetProviderTypeMapping 根据供应商类型获取映射
func (r *PostgresWorkOrderRepository) GetProviderTypeMapping(ctx context.Context, providerType int, provider string) (*model.WorkOrderTypeMapping, error) {
	query := `
		SELECT id, unified_type, unified_name, provider, provider_type, provider_name, is_supported, description, created_at, updated_at
		FROM work_order_type_mappings
		WHERE provider_type = $1 AND provider = $2
	`

	var mapping model.WorkOrderTypeMapping
	err := r.db.QueryRowContext(ctx, query, providerType, provider).Scan(
		&mapping.ID,
		&mapping.UnifiedType,
		&mapping.UnifiedName,
		&mapping.Provider,
		&mapping.ProviderType,
		&mapping.ProviderName,
		&mapping.IsSupported,
		&mapping.Description,
		&mapping.CreatedAt,
		&mapping.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("供应商工单类型映射不存在: provider_type=%d, provider=%s", providerType, provider)
		}
		r.logger.Error("获取供应商工单类型映射失败", zap.Error(err))
		return nil, fmt.Errorf("获取供应商工单类型映射失败: %w", err)
	}

	return &mapping, nil
}

// GetProviderStatusMapping 根据供应商状态获取映射
func (r *PostgresWorkOrderRepository) GetProviderStatusMapping(ctx context.Context, providerStatus int, provider string) (*model.WorkOrderStatusMapping, error) {
	query := `
		SELECT id, unified_status, unified_name, provider, provider_status, provider_name, description, created_at, updated_at
		FROM work_order_status_mappings
		WHERE provider_status = $1 AND provider = $2
	`

	var mapping model.WorkOrderStatusMapping
	err := r.db.QueryRowContext(ctx, query, providerStatus, provider).Scan(
		&mapping.ID,
		&mapping.UnifiedStatus,
		&mapping.UnifiedName,
		&mapping.Provider,
		&mapping.ProviderStatus,
		&mapping.ProviderName,
		&mapping.Description,
		&mapping.CreatedAt,
		&mapping.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("供应商工单状态映射不存在: provider_status=%d, provider=%s", providerStatus, provider)
		}
		r.logger.Error("获取供应商工单状态映射失败", zap.Error(err))
		return nil, fmt.Errorf("获取供应商工单状态映射失败: %w", err)
	}

	return &mapping, nil
}

// ListSupportedTypes 获取支持的工单类型列表
func (r *PostgresWorkOrderRepository) ListSupportedTypes(ctx context.Context, provider string) ([]model.WorkOrderTypeMapping, error) {
	query := `
		SELECT id, unified_type, unified_name, provider, provider_type, provider_name, is_supported, description, created_at, updated_at
		FROM work_order_type_mappings
		WHERE provider = $1 AND is_supported = true
		ORDER BY unified_type ASC
	`

	rows, err := r.db.QueryContext(ctx, query, provider)
	if err != nil {
		r.logger.Error("获取支持的工单类型失败", zap.Error(err), zap.String("provider", provider))
		return nil, fmt.Errorf("获取支持的工单类型失败: %w", err)
	}
	defer rows.Close()

	var mappings []model.WorkOrderTypeMapping
	for rows.Next() {
		var mapping model.WorkOrderTypeMapping
		err := rows.Scan(
			&mapping.ID,
			&mapping.UnifiedType,
			&mapping.UnifiedName,
			&mapping.Provider,
			&mapping.ProviderType,
			&mapping.ProviderName,
			&mapping.IsSupported,
			&mapping.Description,
			&mapping.CreatedAt,
			&mapping.UpdatedAt,
		)
		if err != nil {
			r.logger.Error("扫描工单类型映射失败", zap.Error(err))
			return nil, fmt.Errorf("扫描工单类型映射失败: %w", err)
		}
		mappings = append(mappings, mapping)
	}

	return mappings, nil
}

// ListAllSupportedTypes 获取所有供应商支持的工单类型列表
func (r *PostgresWorkOrderRepository) ListAllSupportedTypes(ctx context.Context) ([]model.WorkOrderTypeMapping, error) {
	query := `
		SELECT id, unified_type, unified_name, provider, provider_type, provider_name, is_supported, description, created_at, updated_at
		FROM work_order_type_mappings
		WHERE is_supported = true
		ORDER BY unified_type ASC, provider ASC
	`

	rows, err := r.db.QueryContext(ctx, query)
	if err != nil {
		r.logger.Error("获取所有支持的工单类型失败", zap.Error(err))
		return nil, fmt.Errorf("获取所有支持的工单类型失败: %w", err)
	}
	defer rows.Close()

	var mappings []model.WorkOrderTypeMapping
	for rows.Next() {
		var mapping model.WorkOrderTypeMapping
		err := rows.Scan(
			&mapping.ID,
			&mapping.UnifiedType,
			&mapping.UnifiedName,
			&mapping.Provider,
			&mapping.ProviderType,
			&mapping.ProviderName,
			&mapping.IsSupported,
			&mapping.Description,
			&mapping.CreatedAt,
			&mapping.UpdatedAt,
		)
		if err != nil {
			r.logger.Error("扫描工单类型映射失败", zap.Error(err))
			return nil, fmt.Errorf("扫描工单类型映射失败: %w", err)
		}
		mappings = append(mappings, mapping)
	}

	return mappings, nil
}

// 🔥 新增：检查用户自定义工单ID是否存在
func (r *PostgresWorkOrderRepository) ExistsByCustomerWorkOrderID(ctx context.Context, userID, customerWorkOrderID string) (bool, error) {
	query := `
		SELECT COUNT(*) 
		FROM work_orders 
		WHERE user_id = $1 AND customer_work_order_id = $2 AND deleted_at IS NULL
	`

	var count int
	err := r.db.QueryRowContext(ctx, query, userID, customerWorkOrderID).Scan(&count)
	if err != nil {
		r.logger.Error("检查用户自定义工单ID存在性失败",
			zap.Error(err),
			zap.String("user_id", userID),
			zap.String("customer_work_order_id", customerWorkOrderID))
		return false, fmt.Errorf("检查用户自定义工单ID存在性失败: %w", err)
	}

	return count > 0, nil
}
