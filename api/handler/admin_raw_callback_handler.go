package handler

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/service"
	"go.uber.org/zap"
)

// AdminRawCallbackHandler 管理员原始回调数据处理器
type AdminRawCallbackHandler struct {
	rawCallbackService *service.RawCallbackService
	callbackService    *service.UnifiedCallbackService
	logger             *zap.Logger
}

// NewAdminRawCallbackHandler 创建管理员原始回调数据处理器
func NewAdminRawCallbackHandler(
	rawCallbackService *service.RawCallbackService,
	callbackService *service.UnifiedCallbackService,
	logger *zap.Logger,
) *AdminRawCallbackHandler {
	return &AdminRawCallbackHandler{
		rawCallbackService: rawCallbackService,
		callbackService:    callbackService,
		logger:             logger,
	}
}

// RawCallbackRecord 原始回调记录
type RawCallbackRecord struct {
	ID           string                 `json:"id"`
	Provider     string                 `json:"provider"`
	RawBody      string                 `json:"raw_body"`
	ParsedData   map[string]interface{} `json:"parsed_data,omitempty"`
	EventType    string                 `json:"event_type,omitempty"`
	OrderNo      string                 `json:"order_no,omitempty"`
	TrackingNo   string                 `json:"tracking_no,omitempty"`
	WorkOrderID  string                 `json:"work_order_id,omitempty"`  // 🔥 新增：工单ID
	ProcessedAt  *time.Time             `json:"processed_at,omitempty"`
	ProcessError string                 `json:"process_error,omitempty"`
	ReceivedAt   time.Time              `json:"received_at"`
	CreatedAt    time.Time              `json:"created_at"`
}

// RawCallbackListParams 原始回调列表查询参数
type RawCallbackListParams struct {
	Page         int    `form:"page"`
	PageSize     int    `form:"page_size"`
	Provider     string `form:"provider"`
	EventType    string `form:"event_type"`
	OrderNo      string `form:"order_no"`
	TrackingNo   string `form:"tracking_no"`
	WorkOrderID  string `form:"work_order_id"`  // 🔥 新增：工单ID搜索
	StartTime    string `form:"start_time"`
	EndTime      string `form:"end_time"`
	Status       string `form:"status"` // success, failed, pending
}

// RawCallbackListResponse 原始回调列表响应
type RawCallbackListResponse struct {
	Records  []RawCallbackRecord `json:"records"`
	Total    int64               `json:"total"`
	Page     int                 `json:"page"`
	PageSize int                 `json:"page_size"`
}

// RawCallbackStatistics 原始回调统计
type RawCallbackStatistics struct {
	TotalRecords   int64                          `json:"total_records"`
	SuccessRecords int64                          `json:"success_records"`
	FailedRecords  int64                          `json:"failed_records"`
	PendingRecords int64                          `json:"pending_records"`
	SuccessRate    float64                        `json:"success_rate"`
	ProviderStats  map[string]ProviderStatistics  `json:"provider_stats"`
	EventTypeStats map[string]EventTypeStatistics `json:"event_type_stats"`
	DailyStats     []DailyStatistics              `json:"daily_stats"`
}

// ProviderStatistics 供应商统计
type ProviderStatistics struct {
	Total   int64 `json:"total"`
	Success int64 `json:"success"`
	Failed  int64 `json:"failed"`
	Pending int64 `json:"pending"`
}

// EventTypeStatistics 事件类型统计
type EventTypeStatistics struct {
	Total   int64 `json:"total"`
	Success int64 `json:"success"`
	Failed  int64 `json:"failed"`
	Pending int64 `json:"pending"`
}

// DailyStatistics 每日统计
type DailyStatistics struct {
	Date    string `json:"date"`
	Total   int64  `json:"total"`
	Success int64  `json:"success"`
	Failed  int64  `json:"failed"`
	Pending int64  `json:"pending"`
}

// BatchRetryRequest 批量重推请求
type BatchRetryRequest struct {
	RecordIDs []string `json:"record_ids" binding:"required,min=1"`
}

// BatchRetryByConditionRequest 按条件批量重推请求
type BatchRetryByConditionRequest struct {
	Provider  string `json:"provider"`
	EventType string `json:"event_type"`
	StartTime string `json:"start_time"`
	EndTime   string `json:"end_time"`
	Status    string `json:"status"`
}

// GetRawCallbackList 获取原始回调列表
func (h *AdminRawCallbackHandler) GetRawCallbackList(c *gin.Context) {
	var params RawCallbackListParams
	if err := c.ShouldBindQuery(&params); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "参数错误: " + err.Error(),
		})
		return
	}

	// 设置默认值
	if params.Page == 0 {
		params.Page = 1
	}
	if params.PageSize == 0 {
		params.PageSize = 50
	}

	// 构建查询条件
	conditions := make(map[string]interface{})
	if params.Provider != "" {
		conditions["provider"] = params.Provider
	}
	if params.OrderNo != "" {
		// 支持模糊搜索订单号
		conditions["order_no_like"] = params.OrderNo
	}
	if params.TrackingNo != "" {
		conditions["tracking_no_like"] = params.TrackingNo
	}
	if params.WorkOrderID != "" {
		// 🔥 新增：支持工单ID搜索
		conditions["work_order_id_like"] = params.WorkOrderID
	}
	if params.StartTime != "" {
		if startTime, err := time.Parse("2006-01-02 15:04:05", params.StartTime); err == nil {
			conditions["start_time"] = startTime
		}
	}
	if params.EndTime != "" {
		if endTime, err := time.Parse("2006-01-02 15:04:05", params.EndTime); err == nil {
			conditions["end_time"] = endTime
		}
	}

	// 调用服务获取数据
	records, total, err := h.rawCallbackService.GetRawCallbackList(
		params.Page, params.PageSize, conditions,
	)
	if err != nil {
		h.logger.Error("获取原始回调列表失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取原始回调列表失败",
		})
		return
	}

	// 转换为响应格式
	responseRecords := make([]RawCallbackRecord, len(records))
	for i, record := range records {
		responseRecords[i] = h.convertToRawCallbackRecord(record)
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": RawCallbackListResponse{
			Records:  responseRecords,
			Total:    total,
			Page:     params.Page,
			PageSize: params.PageSize,
		},
	})
}

// GetRawCallbackDetail 获取原始回调详情
func (h *AdminRawCallbackHandler) GetRawCallbackDetail(c *gin.Context) {
	recordID := c.Param("id")
	if recordID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "记录ID不能为空",
		})
		return
	}

	record, err := h.rawCallbackService.GetRawCallbackByID(recordID)
	if err != nil {
		h.logger.Error("获取原始回调详情失败", zap.String("record_id", recordID), zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取原始回调详情失败",
		})
		return
	}

	if record == nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"message": "回调记录不存在",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    h.convertToRawCallbackRecord(*record),
	})
}

// RetryRawCallback 重推单个原始回调
func (h *AdminRawCallbackHandler) RetryRawCallback(c *gin.Context) {
	recordID := c.Param("id")
	if recordID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "记录ID不能为空",
		})
		return
	}

	// 获取原始回调记录
	record, err := h.rawCallbackService.GetRawCallbackByID(recordID)
	if err != nil {
		h.logger.Error("获取原始回调记录失败", zap.String("record_id", recordID), zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取原始回调记录失败",
		})
		return
	}

	if record == nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"message": "回调记录不存在",
		})
		return
	}

	// 重新处理回调
	err = h.callbackService.ReprocessRawCallback(record.Provider, []byte(record.RawBody))
	if err != nil {
		h.logger.Error("重推回调失败",
			zap.String("record_id", recordID),
			zap.String("provider", record.Provider),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "重推回调失败: " + err.Error(),
		})
		return
	}

	// 记录操作日志
	h.logger.Info("管理员重推原始回调成功",
		zap.String("record_id", recordID),
		zap.String("provider", record.Provider),
		zap.String("admin_user", c.GetString("user_id")))

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "重推回调成功",
	})
}

// convertToRawCallbackRecord 转换为原始回调记录格式
func (h *AdminRawCallbackHandler) convertToRawCallbackRecord(record model.RawCallbackData) RawCallbackRecord {
	result := RawCallbackRecord{
		ID:       record.ID,
		Provider: record.Provider,
		RawBody:  record.RawBody,
	}

	// 处理可能为nil的时间字段
	if record.ReceivedAt != nil {
		result.ReceivedAt = *record.ReceivedAt
	}
	if record.CreatedAt != nil {
		result.CreatedAt = *record.CreatedAt
	}

	// 尝试解析原始数据以提取关键信息
	if record.RawBody != "" {
		if parsedData, eventType, orderNo, trackingNo, workOrderID := h.parseRawCallbackData(record.Provider, record.RawBody); eventType != "" || orderNo != "" || trackingNo != "" || workOrderID != "" {
			result.ParsedData = parsedData
			result.EventType = eventType
			result.OrderNo = orderNo
			result.TrackingNo = trackingNo
			result.WorkOrderID = workOrderID  // 🔥 新增：工单ID

			// 🔍 调试日志
			h.logger.Debug("解析回调数据成功",
				zap.String("provider", record.Provider),
				zap.String("event_type", eventType),
				zap.String("order_no", orderNo),
				zap.String("tracking_no", trackingNo),
				zap.String("work_order_id", workOrderID))
		} else {
			// 🔍 调试日志 - 解析失败
			preview := record.RawBody
			if len(preview) > 100 {
				preview = preview[:100] + "..."
			}
			h.logger.Debug("解析回调数据失败",
				zap.String("provider", record.Provider),
				zap.String("raw_body_preview", preview))
		}
	}

	return result
}

// parseRawCallbackData 解析原始回调数据
func (h *AdminRawCallbackHandler) parseRawCallbackData(provider, rawBody string) (map[string]interface{}, string, string, string, string) {
	var parsedData map[string]interface{}
	var eventType, orderNo, trackingNo, workOrderID string

	switch provider {
	case "cainiao":
		eventType, orderNo, trackingNo, workOrderID = h.parseCainiaoCallback(rawBody)
	case "kuaidi100":
		eventType, orderNo, trackingNo, workOrderID = h.parseKuaidi100Callback(rawBody)
	case "yida":
		eventType, orderNo, trackingNo, workOrderID = h.parseYidaCallback(rawBody)
	case "yuntong":
		eventType, orderNo, trackingNo, workOrderID = h.parseYuntongCallback(rawBody)
	case "kuaidiniao":
		eventType, orderNo, trackingNo, workOrderID = h.parseKuaidiniaoCallback(rawBody)
	}

	return parsedData, eventType, orderNo, trackingNo, workOrderID
}

// parseCainiaoCallback 解析菜鸟回调数据
func (h *AdminRawCallbackHandler) parseCainiaoCallback(rawBody string) (string, string, string, string) {
	// 菜鸟回调是URL编码的表单数据
	if strings.Contains(rawBody, "logistics_interface=") {
		// 提取logistics_interface参数
		start := strings.Index(rawBody, "logistics_interface=")
		if start != -1 {
			start += len("logistics_interface=")
			end := strings.Index(rawBody[start:], "&")
			if end == -1 {
				end = len(rawBody) - start
			}

			// URL解码
			if logisticsInterface, err := url.QueryUnescape(rawBody[start : start+end]); err == nil {
				// JSON解析
				var data map[string]interface{}
				if err := json.Unmarshal([]byte(logisticsInterface), &data); err == nil {
					if orderEvent, ok := data["orderEvent"].(map[string]interface{}); ok {
						if eventType, ok := orderEvent["eventType"].(string); ok {
							var orderNo, trackingNo string

							if externalOrder, ok := data["externalOrder"].(map[string]interface{}); ok {
								if orderId, ok := externalOrder["orderId"].(string); ok {
									orderNo = orderId
								}
							}

							if eventData, ok := orderEvent["eventData"].(map[string]interface{}); ok {
								if mailNo, ok := eventData["mailNo"].(string); ok {
									trackingNo = mailNo
								}
							}

							return eventType, orderNo, trackingNo, ""  // 菜鸟回调暂不包含工单ID
						}
					}
				}
			}
		}
	}
	return "", "", "", ""
}

// parseKuaidi100Callback 解析快递100回调数据
func (h *AdminRawCallbackHandler) parseKuaidi100Callback(rawBody string) (string, string, string, string) {
	// 快递100回调是URL编码的表单数据：param=...&sign=...&taskId=...
	var eventType, orderNo, trackingNo, workOrderID string

	// 解析URL编码的表单数据
	if strings.Contains(rawBody, "param=") {
		// 提取param参数
		start := strings.Index(rawBody, "param=")
		if start != -1 {
			start += len("param=")
			end := strings.Index(rawBody[start:], "&")
			if end == -1 {
				end = len(rawBody) - start
			}

			// URL解码param参数
			if paramData, err := url.QueryUnescape(rawBody[start : start+end]); err == nil {
				// JSON解析param内容
				var data map[string]interface{}
				if err := json.Unmarshal([]byte(paramData), &data); err == nil {
					// 提取状态信息
					if status, ok := data["status"].(string); ok {
						eventType = "status_" + status
					}

					// 提取订单信息
					if dataObj, ok := data["data"].(map[string]interface{}); ok {
						if thirdOrderId, ok := dataObj["thirdOrderId"].(string); ok {
							orderNo = thirdOrderId
						}
					}

					// 提取运单号
					if kuaidinum, ok := data["kuaidinum"].(string); ok {
						trackingNo = kuaidinum
					}
					
					// 🔥 提取工单ID - 快递100工单回调
					if workorderId, ok := data["workorderId"]; ok {
						if workorderIdFloat, ok := workorderId.(float64); ok {
							workOrderID = fmt.Sprintf("%.0f", workorderIdFloat)
						} else if workorderIdStr, ok := workorderId.(string); ok {
							workOrderID = workorderIdStr
						}
					}
				}
			}
		}

		// 提取taskId作为备用订单号
		if orderNo == "" && strings.Contains(rawBody, "taskId=") {
			start := strings.Index(rawBody, "taskId=")
			if start != -1 {
				start += len("taskId=")
				end := strings.Index(rawBody[start:], "&")
				if end == -1 {
					end = len(rawBody) - start
				}
				orderNo = rawBody[start : start+end]
			}
		}
	}

	return eventType, orderNo, trackingNo, workOrderID
}

// parseYidaCallback 解析易达回调数据
func (h *AdminRawCallbackHandler) parseYidaCallback(rawBody string) (string, string, string, string) {
	// 易达回调通常是JSON格式
	var data map[string]interface{}
	if err := json.Unmarshal([]byte(rawBody), &data); err == nil {
		var eventType, orderNo, trackingNo, workOrderID string

		if pushType, ok := data["pushType"].(float64); ok {
			eventType = fmt.Sprintf("push_type_%d", int(pushType))
		}

		if thirdNo, ok := data["thirdNo"].(string); ok {
			orderNo = thirdNo
		}

		if deliveryId, ok := data["deliveryId"].(string); ok {
			trackingNo = deliveryId
		}

		// 🔥 提取工单ID - 易达工单回调
		if taskNo, ok := data["taskNo"]; ok {
			if taskNoStr, ok := taskNo.(string); ok {
				workOrderID = taskNoStr
			} else if taskNoFloat, ok := taskNo.(float64); ok {
				workOrderID = fmt.Sprintf("%.0f", taskNoFloat)
			}
		}

		return eventType, orderNo, trackingNo, workOrderID
	}
	return "", "", "", ""
}

// parseYuntongCallback 解析云通回调数据
func (h *AdminRawCallbackHandler) parseYuntongCallback(rawBody string) (string, string, string, string) {
	// 云通回调是嵌套JSON格式：外层有RequestData字段，内层是字符串化的JSON
	var data map[string]interface{}
	if err := json.Unmarshal([]byte(rawBody), &data); err == nil {
		var eventType, orderNo, trackingNo, workOrderID string

		// 提取RequestData字段
		if requestDataStr, ok := data["RequestData"].(string); ok {
			// 解析内层JSON
			var requestData map[string]interface{}
			if err := json.Unmarshal([]byte(requestDataStr), &requestData); err == nil {
				// 提取Data数组
				if dataArray, ok := requestData["Data"].([]interface{}); ok && len(dataArray) > 0 {
					if firstData, ok := dataArray[0].(map[string]interface{}); ok {
						// 提取状态信息
						if state, ok := firstData["State"].(float64); ok {
							eventType = fmt.Sprintf("state_%d", int(state))
						}

						// 提取订单号
						if orderCode, ok := firstData["OrderCode"].(string); ok {
							orderNo = orderCode
						}

						// 提取运单号
						if logisticCode, ok := firstData["LogisticCode"].(string); ok {
							trackingNo = logisticCode
						}
					}
				}
			}
		}

		return eventType, orderNo, trackingNo, workOrderID
	}
	return "", "", "", ""
}

// parseKuaidiniaoCallback 解析快递鸟回调数据
func (h *AdminRawCallbackHandler) parseKuaidiniaoCallback(rawBody string) (string, string, string, string) {
	// 快递鸟回调是URL编码的表单数据：RequestData=...&DataSign=...&RequestType=...
	var eventType, orderNo, trackingNo, workOrderID string

	// 解析URL编码的表单数据
	if strings.Contains(rawBody, "RequestData=") {
		// 提取RequestData参数
		start := strings.Index(rawBody, "RequestData=")
		if start != -1 {
			start += len("RequestData=")
			end := strings.Index(rawBody[start:], "&")
			if end == -1 {
				end = len(rawBody) - start
			}

			// URL解码RequestData参数
			if requestData, err := url.QueryUnescape(rawBody[start : start+end]); err == nil {
				// JSON解析RequestData内容
				var data map[string]interface{}
				if err := json.Unmarshal([]byte(requestData), &data); err == nil {
					// 提取Data数组
					if dataArray, ok := data["Data"].([]interface{}); ok && len(dataArray) > 0 {
						if firstData, ok := dataArray[0].(map[string]interface{}); ok {
							// 提取状态信息
							if state, ok := firstData["State"].(string); ok {
								eventType = "state_" + state
							}

							// 提取订单号
							if orderCode, ok := firstData["OrderCode"].(string); ok {
								orderNo = orderCode
							}

							// 提取运单号
							if logisticCode, ok := firstData["LogisticCode"].(string); ok {
								trackingNo = logisticCode
							}

							// 🔥 提取工单ID - 快递鸟工单回调
							if ticketId, ok := firstData["TicketId"].(string); ok {
								workOrderID = ticketId
							} else if ticketNumber, ok := firstData["TicketNumber"].(string); ok {
								workOrderID = ticketNumber
							}
						}
					}
				}
			}
		}
	}

	return eventType, orderNo, trackingNo, workOrderID
}

// BatchRetryRawCallbacks 批量重推原始回调
func (h *AdminRawCallbackHandler) BatchRetryRawCallbacks(c *gin.Context) {
	var req BatchRetryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "参数错误: " + err.Error(),
		})
		return
	}

	if len(req.RecordIDs) > 100 {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "批量重推数量不能超过100条",
		})
		return
	}

	successCount := 0
	failedCount := 0
	var errors []string

	for _, recordID := range req.RecordIDs {
		// 获取原始回调记录
		record, err := h.rawCallbackService.GetRawCallbackByID(recordID)
		if err != nil {
			failedCount++
			errors = append(errors, fmt.Sprintf("记录%s: 获取失败 - %s", recordID, err.Error()))
			continue
		}

		if record == nil {
			failedCount++
			errors = append(errors, fmt.Sprintf("记录%s: 不存在", recordID))
			continue
		}

		// 重新处理回调
		err = h.callbackService.ReprocessRawCallback(record.Provider, []byte(record.RawBody))
		if err != nil {
			failedCount++
			errors = append(errors, fmt.Sprintf("记录%s: 重推失败 - %s", recordID, err.Error()))
			h.logger.Error("批量重推回调失败",
				zap.String("record_id", recordID),
				zap.String("provider", record.Provider),
				zap.Error(err))
		} else {
			successCount++
			h.logger.Info("批量重推原始回调成功",
				zap.String("record_id", recordID),
				zap.String("provider", record.Provider),
				zap.String("admin_user", c.GetString("user_id")))
		}
	}

	// 记录批量操作日志
	h.logger.Info("管理员批量重推原始回调完成",
		zap.Int("total", len(req.RecordIDs)),
		zap.Int("success", successCount),
		zap.Int("failed", failedCount),
		zap.String("admin_user", c.GetString("user_id")))

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"total":         len(req.RecordIDs),
			"success_count": successCount,
			"failed_count":  failedCount,
			"errors":        errors,
		},
		"message": fmt.Sprintf("批量重推完成，成功%d条，失败%d条", successCount, failedCount),
	})
}

// BatchRetryByCondition 按条件批量重推
func (h *AdminRawCallbackHandler) BatchRetryByCondition(c *gin.Context) {
	var req BatchRetryByConditionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "参数错误: " + err.Error(),
		})
		return
	}

	// 构建查询条件
	conditions := make(map[string]interface{})
	if req.Provider != "" {
		conditions["provider"] = req.Provider
	}
	if req.StartTime != "" {
		if startTime, err := time.Parse("2006-01-02 15:04:05", req.StartTime); err == nil {
			conditions["start_time"] = startTime
		}
	}
	if req.EndTime != "" {
		if endTime, err := time.Parse("2006-01-02 15:04:05", req.EndTime); err == nil {
			conditions["end_time"] = endTime
		}
	}

	// 获取符合条件的记录（限制最多1000条）
	records, total, err := h.rawCallbackService.GetRawCallbackList(1, 1000, conditions)
	if err != nil {
		h.logger.Error("获取符合条件的原始回调记录失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取符合条件的记录失败",
		})
		return
	}

	if total > 1000 {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": fmt.Sprintf("符合条件的记录过多(%d条)，请缩小查询范围", total),
		})
		return
	}

	successCount := 0
	failedCount := 0
	var errors []string

	for _, record := range records {
		// 重新处理回调
		err = h.callbackService.ReprocessRawCallback(record.Provider, []byte(record.RawBody))
		if err != nil {
			failedCount++
			errors = append(errors, fmt.Sprintf("记录%s: 重推失败 - %s", record.ID, err.Error()))
			h.logger.Error("按条件批量重推回调失败",
				zap.String("record_id", record.ID),
				zap.String("provider", record.Provider),
				zap.Error(err))
		} else {
			successCount++
		}
	}

	// 记录批量操作日志
	h.logger.Info("管理员按条件批量重推原始回调完成",
		zap.Int64("total", total),
		zap.Int("success", successCount),
		zap.Int("failed", failedCount),
		zap.Any("conditions", conditions),
		zap.String("admin_user", c.GetString("user_id")))

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"total":         total,
			"success_count": successCount,
			"failed_count":  failedCount,
			"errors":        errors,
		},
		"message": fmt.Sprintf("按条件批量重推完成，成功%d条，失败%d条", successCount, failedCount),
	})
}

// GetRawCallbackStatistics 获取原始回调统计
func (h *AdminRawCallbackHandler) GetRawCallbackStatistics(c *gin.Context) {
	provider := c.Query("provider")
	startTime := c.Query("start_time")
	endTime := c.Query("end_time")

	// 构建查询条件
	conditions := make(map[string]interface{})
	if provider != "" {
		conditions["provider"] = provider
	}
	if startTime != "" {
		if st, err := time.Parse("2006-01-02 15:04:05", startTime); err == nil {
			conditions["start_time"] = st
		}
	}
	if endTime != "" {
		if et, err := time.Parse("2006-01-02 15:04:05", endTime); err == nil {
			conditions["end_time"] = et
		}
	}

	// 获取统计数据
	stats, err := h.rawCallbackService.GetRawCallbackStatistics(conditions)
	if err != nil {
		h.logger.Error("获取原始回调统计失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取统计数据失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    stats,
	})
}

// ExportRawCallbacks 导出原始回调数据
func (h *AdminRawCallbackHandler) ExportRawCallbacks(c *gin.Context) {
	var params RawCallbackListParams
	if err := c.ShouldBindQuery(&params); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "参数错误: " + err.Error(),
		})
		return
	}

	// 构建查询条件
	conditions := make(map[string]interface{})
	if params.Provider != "" {
		conditions["provider"] = params.Provider
	}
	if params.OrderNo != "" {
		conditions["order_no_like"] = params.OrderNo
	}
	if params.TrackingNo != "" {
		conditions["tracking_no_like"] = params.TrackingNo
	}
	if params.WorkOrderID != "" {
		conditions["work_order_id_like"] = params.WorkOrderID
	}
	if params.StartTime != "" {
		if startTime, err := time.Parse("2006-01-02 15:04:05", params.StartTime); err == nil {
			conditions["start_time"] = startTime
		}
	}
	if params.EndTime != "" {
		if endTime, err := time.Parse("2006-01-02 15:04:05", params.EndTime); err == nil {
			conditions["end_time"] = endTime
		}
	}

	// 获取所有符合条件的记录（限制最多10000条）
	records, total, err := h.rawCallbackService.GetRawCallbackList(1, 10000, conditions)
	if err != nil {
		h.logger.Error("获取导出数据失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取导出数据失败",
		})
		return
	}

	if total > 10000 {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": fmt.Sprintf("导出数据过多(%d条)，请缩小查询范围", total),
		})
		return
	}

	// 生成CSV数据
	csvData := h.generateCSVData(records)

	// 设置响应头
	filename := fmt.Sprintf("raw_callbacks_%s.csv", time.Now().Format("20060102_150405"))
	c.Header("Content-Type", "text/csv; charset=utf-8")
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", filename))

	// 写入BOM以支持Excel正确显示中文
	c.Writer.Write([]byte{0xEF, 0xBB, 0xBF})
	c.String(http.StatusOK, csvData)

	// 记录导出操作日志
	h.logger.Info("管理员导出原始回调数据",
		zap.Int64("total", total),
		zap.Any("conditions", conditions),
		zap.String("admin_user", c.GetString("user_id")))
}

// generateCSVData 生成CSV数据
func (h *AdminRawCallbackHandler) generateCSVData(records []model.RawCallbackData) string {
	var csvLines []string

	// CSV头部
	csvLines = append(csvLines, "ID,供应商,事件类型,订单号,运单号,工单ID,接收时间,原始数据预览")

	// CSV数据行
	for _, record := range records {
		convertedRecord := h.convertToRawCallbackRecord(record)

		// 原始数据预览（限制长度）
		rawDataPreview := convertedRecord.RawBody
		if len(rawDataPreview) > 200 {
			rawDataPreview = rawDataPreview[:200] + "..."
		}
		// 转义CSV中的特殊字符
		rawDataPreview = strings.ReplaceAll(rawDataPreview, "\"", "\"\"")
		rawDataPreview = strings.ReplaceAll(rawDataPreview, "\n", " ")
		rawDataPreview = strings.ReplaceAll(rawDataPreview, "\r", " ")

		line := fmt.Sprintf("%s,%s,%s,%s,%s,%s,%s,\"%s\"",
			convertedRecord.ID,
			convertedRecord.Provider,
			convertedRecord.EventType,
			convertedRecord.OrderNo,
			convertedRecord.TrackingNo,
			convertedRecord.WorkOrderID,  // 🔥 新增：工单ID列
			convertedRecord.ReceivedAt.Format("2006-01-02 15:04:05"),
			rawDataPreview,
		)
		csvLines = append(csvLines, line)
	}

	return strings.Join(csvLines, "\n")
}
