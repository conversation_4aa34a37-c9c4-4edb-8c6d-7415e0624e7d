package handler

import (
	"io"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/service"
	"go.uber.org/zap"
)

// WorkOrderHandler 工单处理器
type WorkOrderHandler struct {
	workOrderService service.WorkOrderService
	logger           *zap.Logger
}

// NewWorkOrderHandler 创建工单处理器
func NewWorkOrderHandler(workOrderService service.WorkOrderService, logger *zap.Logger) *WorkOrderHandler {
	return &WorkOrderHandler{
		workOrderService: workOrderService,
		logger:           logger,
	}
}

// GetService 获取工单服务（用于依赖注入）
func (h *WorkOrderHandler) GetService() service.WorkOrderService {
	return h.workOrderService
}

// CreateWorkOrder 创建工单（统一使用智能创建方式）
// @Summary 智能创建工单
// @Description 用户友好的智能工单创建，自动识别供应商和处理映射
// @Tags 工单管理
// @Accept json
// @Produce json
// @Param request body model.SmartCreateWorkOrderRequest true "智能创建工单请求"
// @Success 200 {object} model.WorkOrderResponse
// @Failure 400 {object} model.WorkOrderResponse
// @Failure 500 {object} model.WorkOrderResponse
// @Router /api/v1/workorders [post]
func (h *WorkOrderHandler) CreateWorkOrder(c *gin.Context) {
	// 1. 获取用户ID
	userID, exists := c.Get("userID")
	if !exists {
		h.respondError(c, http.StatusUnauthorized, "用户未认证")
		return
	}

	// 2. 解析请求参数
	var req model.SmartCreateWorkOrderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("解析智能创建工单请求失败", zap.Error(err))
		h.respondError(c, http.StatusBadRequest, "请求参数格式错误: "+err.Error())
		return
	}

	// 3. 调用服务智能创建工单
	workOrder, err := h.workOrderService.CreateWorkOrder(c.Request.Context(), userID.(string), &req)
	if err != nil {
		h.logger.Error("智能创建工单失败",
			zap.Error(err),
			zap.String("user_id", userID.(string)),
			zap.Int("work_order_type", req.WorkOrderType))
		h.respondError(c, http.StatusBadRequest, err.Error())
		return
	}

	// 4. 返回成功响应
	h.respondSuccess(c, workOrder)
}

// GetWorkOrder 获取工单详情
// @Summary 获取工单详情
// @Description 根据工单ID获取工单详情
// @Tags 工单管理
// @Accept json
// @Produce json
// @Param id path string true "工单ID"
// @Success 200 {object} model.WorkOrderResponse
// @Failure 400 {object} model.WorkOrderResponse
// @Failure 404 {object} model.WorkOrderResponse
// @Failure 500 {object} model.WorkOrderResponse
// @Router /api/v1/workorders/{id} [get]
func (h *WorkOrderHandler) GetWorkOrder(c *gin.Context) {
	// 1. 获取用户ID
	userID, exists := c.Get("userID")
	if !exists {
		h.respondError(c, http.StatusUnauthorized, "用户未认证")
		return
	}

	// 2. 解析工单ID
	workOrderIDStr := c.Param("id")
	workOrderID, err := uuid.Parse(workOrderIDStr)
	if err != nil {
		h.respondError(c, http.StatusBadRequest, "工单ID格式错误")
		return
	}

	// 3. 调用服务获取工单
	workOrder, err := h.workOrderService.GetWorkOrder(c.Request.Context(), userID.(string), workOrderID)
	if err != nil {
		h.logger.Error("获取工单失败",
			zap.Error(err),
			zap.String("user_id", userID.(string)),
			zap.String("work_order_id", workOrderIDStr))
		h.respondError(c, http.StatusNotFound, "工单不存在或无权访问")
		return
	}

	// 4. 返回成功响应
	h.respondSuccess(c, workOrder)
}

// ListWorkOrders 获取工单列表
// @Summary 获取工单列表
// @Description 获取用户的工单列表
// @Tags 工单管理
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Param provider query string false "供应商"
// @Param status query int false "工单状态"
// @Param work_order_type query int false "工单类型"
// @Param order_no query string false "订单号"
// @Param tracking_no query string false "运单号"
// @Param start_date query string false "开始日期"
// @Param end_date query string false "结束日期"
// @Success 200 {object} model.WorkOrderResponse
// @Failure 400 {object} model.WorkOrderResponse
// @Failure 500 {object} model.WorkOrderResponse
// @Router /api/v1/workorders [get]
func (h *WorkOrderHandler) ListWorkOrders(c *gin.Context) {
	// 1. 获取用户ID
	userID, exists := c.Get("userID")
	if !exists {
		h.respondError(c, http.StatusUnauthorized, "用户未认证")
		return
	}

	// 2. 解析查询参数
	var req model.WorkOrderListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		h.logger.Error("解析工单列表请求失败", zap.Error(err))
		h.respondError(c, http.StatusBadRequest, "请求参数格式错误: "+err.Error())
		return
	}

	// 3. 调用服务获取列表
	response, err := h.workOrderService.ListWorkOrders(c.Request.Context(), userID.(string), &req)
	if err != nil {
		h.logger.Error("获取工单列表失败",
			zap.Error(err),
			zap.String("user_id", userID.(string)))
		h.respondError(c, http.StatusInternalServerError, "获取工单列表失败: "+err.Error())
		return
	}

	// 4. 返回成功响应
	h.respondSuccess(c, response)
}

// DeleteWorkOrder 删除工单
// @Summary 删除工单
// @Description 删除指定的工单
// @Tags 工单管理
// @Accept json
// @Produce json
// @Param id path string true "工单ID"
// @Success 200 {object} model.WorkOrderResponse
// @Failure 400 {object} model.WorkOrderResponse
// @Failure 403 {object} model.WorkOrderResponse
// @Failure 404 {object} model.WorkOrderResponse
// @Failure 500 {object} model.WorkOrderResponse
// @Router /api/v1/workorders/{id} [delete]
func (h *WorkOrderHandler) DeleteWorkOrder(c *gin.Context) {
	// 1. 获取用户ID
	userID, exists := c.Get("userID")
	if !exists {
		h.respondError(c, http.StatusUnauthorized, "用户未认证")
		return
	}

	// 2. 解析工单ID
	workOrderIDStr := c.Param("id")
	workOrderID, err := uuid.Parse(workOrderIDStr)
	if err != nil {
		h.respondError(c, http.StatusBadRequest, "工单ID格式错误")
		return
	}

	// 3. 调用服务删除工单
	err = h.workOrderService.DeleteWorkOrder(c.Request.Context(), userID.(string), workOrderID)
	if err != nil {
		h.logger.Error("删除工单失败",
			zap.Error(err),
			zap.String("user_id", userID.(string)),
			zap.String("work_order_id", workOrderIDStr))

		// 根据错误类型返回不同的HTTP状态码
		if err.Error() == "工单不存在" {
			h.respondError(c, http.StatusNotFound, "工单不存在")
		} else if err.Error() == "无权删除该工单" {
			h.respondError(c, http.StatusForbidden, "无权删除该工单")
		} else if strings.Contains(err.Error(), "工单状态不允许删除") {
			h.respondError(c, http.StatusBadRequest, err.Error())
		} else if strings.Contains(err.Error(), "创建超过24小时") {
			h.respondError(c, http.StatusBadRequest, err.Error())
		} else {
			h.respondError(c, http.StatusInternalServerError, "删除工单失败: "+err.Error())
		}
		return
	}

	// 4. 返回成功响应
	h.respondSuccess(c, map[string]interface{}{
		"message": "工单删除成功",
	})
}

// ReplyWorkOrder 回复工单
// @Summary 回复工单
// @Description 对工单进行回复
// @Tags 工单管理
// @Accept json
// @Produce json
// @Param id path string true "工单ID"
// @Param request body model.ReplyWorkOrderRequest true "回复工单请求"
// @Success 200 {object} model.WorkOrderResponse
// @Failure 400 {object} model.WorkOrderResponse
// @Failure 404 {object} model.WorkOrderResponse
// @Failure 500 {object} model.WorkOrderResponse
// @Router /api/v1/workorders/{id}/replies [post]
func (h *WorkOrderHandler) ReplyWorkOrder(c *gin.Context) {
	// 1. 获取用户ID
	userID, exists := c.Get("userID")
	if !exists {
		h.respondError(c, http.StatusUnauthorized, "用户未认证")
		return
	}

	// 2. 解析工单ID
	workOrderIDStr := c.Param("id")
	workOrderID, err := uuid.Parse(workOrderIDStr)
	if err != nil {
		h.respondError(c, http.StatusBadRequest, "工单ID格式错误")
		return
	}

	// 3. 解析请求参数
	var req model.ReplyWorkOrderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("解析回复工单请求失败", zap.Error(err))
		h.respondError(c, http.StatusBadRequest, "请求参数格式错误: "+err.Error())
		return
	}

	// 4. 调用服务回复工单
	reply, err := h.workOrderService.ReplyWorkOrder(c.Request.Context(), userID.(string), workOrderID, &req)
	if err != nil {
		h.logger.Error("回复工单失败",
			zap.Error(err),
			zap.String("user_id", userID.(string)),
			zap.String("work_order_id", workOrderIDStr))
		h.respondError(c, http.StatusInternalServerError, "回复工单失败: "+err.Error())
		return
	}

	// 5. 返回成功响应
	h.respondSuccess(c, reply)
}

// UploadAttachment 上传附件
// @Summary 上传附件
// @Description 上传工单附件
// @Tags 工单管理
// @Accept multipart/form-data
// @Produce json
// @Param file formData file true "附件文件"
// @Success 200 {object} model.WorkOrderResponse
// @Failure 400 {object} model.WorkOrderResponse
// @Failure 500 {object} model.WorkOrderResponse
// @Router /api/v1/workorders/attachments [post]
func (h *WorkOrderHandler) UploadAttachment(c *gin.Context) {
	// 1. 获取用户ID
	userID, exists := c.Get("userID")
	if !exists {
		h.respondError(c, http.StatusUnauthorized, "用户未认证")
		return
	}

	// 2. 获取上传的文件
	file, header, err := c.Request.FormFile("file")
	if err != nil {
		h.respondError(c, http.StatusBadRequest, "获取上传文件失败: "+err.Error())
		return
	}
	defer file.Close()

	// 3. 读取文件内容
	fileContent, err := io.ReadAll(file)
	if err != nil {
		h.respondError(c, http.StatusBadRequest, "读取文件内容失败: "+err.Error())
		return
	}

	// 4. 验证文件大小（限制为10MB）
	const maxFileSize = 10 * 1024 * 1024 // 10MB
	if len(fileContent) > maxFileSize {
		h.respondError(c, http.StatusBadRequest, "文件大小超过限制（最大10MB）")
		return
	}

	// 5. 调用服务上传附件
	response, err := h.workOrderService.UploadAttachment(c.Request.Context(), userID.(string), header.Filename, fileContent)
	if err != nil {
		h.logger.Error("上传附件失败",
			zap.Error(err),
			zap.String("user_id", userID.(string)),
			zap.String("file_name", header.Filename))
		h.respondError(c, http.StatusInternalServerError, "上传附件失败: "+err.Error())
		return
	}

	// 6. 返回成功响应
	h.respondSuccess(c, response)
}

// GetSupportedTypes 获取支持的工单类型
// @Summary 获取支持的工单类型
// @Description 获取指定供应商支持的工单类型列表，如果不指定供应商则返回所有供应商的类型
// @Tags 工单管理
// @Accept json
// @Produce json
// @Param provider query string false "供应商（可选，不传则返回所有供应商的类型）"
// @Success 200 {object} model.WorkOrderResponse
// @Failure 500 {object} model.WorkOrderResponse
// @Router /api/v1/workorders/types [get]
func (h *WorkOrderHandler) GetSupportedTypes(c *gin.Context) {
	// 1. 获取供应商参数（可选）
	provider := c.Query("provider")

	// 2. 调用服务获取支持的类型
	types, err := h.workOrderService.GetSupportedTypes(c.Request.Context(), provider)
	if err != nil {
		h.logger.Error("获取支持的工单类型失败",
			zap.Error(err),
			zap.String("provider", provider))
		h.respondError(c, http.StatusInternalServerError, "获取支持的工单类型失败: "+err.Error())
		return
	}

	// 3. 返回成功响应
	h.respondSuccess(c, types)
}

// respondSuccess 返回成功响应
func (h *WorkOrderHandler) respondSuccess(c *gin.Context, data interface{}) {
	c.JSON(http.StatusOK, model.WorkOrderResponse{
		Success: true,
		Code:    http.StatusOK,
		Message: "操作成功",
		Data:    data,
	})
}

// respondError 返回错误响应
func (h *WorkOrderHandler) respondError(c *gin.Context, code int, message string) {
	c.JSON(code, model.WorkOrderResponse{
		Success: false,
		Code:    code,
		Message: message,
	})
}
