package router

import (
	"bytes"
	"encoding/json"
	"io"
	"strings"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"github.com/your-org/go-kuaidi/api/handler"
	"github.com/your-org/go-kuaidi/api/middleware"
	"github.com/your-org/go-kuaidi/internal/auth"
	"github.com/your-org/go-kuaidi/internal/service"
)

// CallbackRouterConfig 回调路由配置
type CallbackRouterConfig struct {
	CallbackHandler            *handler.CallbackHandler
	WorkOrderCallbackForwarder *service.WorkOrderCallbackForwarder // 🔥 新增工单回调转发器
	AuthMiddleware             *middleware.AuthMiddleware
	AdminMiddleware            *middleware.AdminMiddleware
	TokenService               auth.TokenService
}

// SetupCallbackRoutes 设置统一回调路由
// 🔥 企业级重构：统一所有供应商回调处理，消除重复路由
func SetupCallbackRoutes(r *gin.Engine, config CallbackRouterConfig) {
	// 🔥 统一供应商回调接收端点（无需认证）
	// 支持物流回调和工单回调的智能分发
	callbackGroup := r.Group("/api/v1/callbacks")
	{
		// 🔥 统一回调端点 - 智能分发到物流或工单处理器
		callbackGroup.POST("/kuaidi100", createUnifiedCallbackHandler("kuaidi100", config))
		callbackGroup.POST("/yida", createUnifiedCallbackHandler("yida", config))
		callbackGroup.POST("/yuntong", createUnifiedCallbackHandler("yuntong", config))
		callbackGroup.POST("/cainiao", createUnifiedCallbackHandler("cainiao", config))
		callbackGroup.POST("/kuaidiniao", createUnifiedCallbackHandler("kuaidiniao", config))

		// 🔥 保持向后兼容 - 支持特定类型的回调端点
		// 物流回调端点
		callbackGroup.POST("/kuaidi100/order", config.CallbackHandler.HandleProviderCallback("kuaidi100"))
		callbackGroup.POST("/kuaidi100/track", config.CallbackHandler.HandleProviderCallback("kuaidi100"))
		callbackGroup.POST("/kuaidi100/ticket", config.CallbackHandler.HandleProviderCallback("kuaidi100"))

		callbackGroup.POST("/yida/status", config.CallbackHandler.HandleProviderCallback("yida"))
		callbackGroup.POST("/yida/billing", config.CallbackHandler.HandleProviderCallback("yida"))
		callbackGroup.POST("/yida/pickup", config.CallbackHandler.HandleProviderCallback("yida"))
		callbackGroup.POST("/yida/ticket", config.CallbackHandler.HandleProviderCallback("yida"))

		callbackGroup.POST("/yuntong/order", config.CallbackHandler.HandleProviderCallback("yuntong"))
		callbackGroup.POST("/yuntong/billing", config.CallbackHandler.HandleProviderCallback("yuntong"))
		callbackGroup.POST("/yuntong/ticket", config.CallbackHandler.HandleProviderCallback("yuntong"))

		callbackGroup.POST("/cainiao/order", config.CallbackHandler.HandleProviderCallback("cainiao"))
		callbackGroup.POST("/cainiao/logistics", config.CallbackHandler.HandleProviderCallback("cainiao"))
		callbackGroup.POST("/cainiao/event", config.CallbackHandler.HandleProviderCallback("cainiao"))

		callbackGroup.POST("/kuaidiniao/order", config.CallbackHandler.HandleProviderCallback("kuaidiniao"))
		callbackGroup.POST("/kuaidiniao/status", config.CallbackHandler.HandleProviderCallback("kuaidiniao"))
		callbackGroup.POST("/kuaidiniao/billing", config.CallbackHandler.HandleProviderCallback("kuaidiniao"))

		// 🔥 工单回调端点（向后兼容）
		callbackGroup.POST("/workorders/kuaidi100", createWorkOrderCallbackHandler("kuaidi100", config))
		callbackGroup.POST("/workorders/yida", createWorkOrderCallbackHandler("yida", config))
		callbackGroup.POST("/workorders/yuntong", createWorkOrderCallbackHandler("yuntong", config))
		callbackGroup.POST("/workorders/cainiao", createWorkOrderCallbackHandler("cainiao", config))
	}

	// 用户回调配置管理端点（需要JWT认证）
	userGroup := r.Group("/api/v1/user/callback")
	userGroup.Use(config.AuthMiddleware.RequireAuth())
	{
		userGroup.GET("/config", config.CallbackHandler.GetUserCallbackConfig)
		userGroup.POST("/config", config.CallbackHandler.UpdateUserCallbackConfig)
		userGroup.GET("/records", config.CallbackHandler.GetUserCallbackRecords)
		userGroup.GET("/statistics", config.CallbackHandler.GetCallbackStatistics)
		userGroup.POST("/test", config.CallbackHandler.TestUserCallback)

		// 🔥 新增：用户端回调重推功能
		userGroup.POST("/retry/:id", config.CallbackHandler.RetryUserCallback)
		userGroup.POST("/batch-retry", config.CallbackHandler.BatchRetryUserCallbacks)
	}

	// 管理端点（需要JWT认证和管理员权限）
	adminGroup := r.Group("/api/v1/admin/callbacks")
	adminGroup.Use(config.AuthMiddleware.RequireAuth())
	adminGroup.Use(config.AdminMiddleware.RequireAdmin())
	{
		// 回调记录管理 - 需要回调读权限
		adminGroup.GET("/records",
			config.AdminMiddleware.RequireResourcePermission("callback", "read"),
			config.CallbackHandler.GetCallbackRecords)

		// 回调重试 - 需要回调写权限
		adminGroup.POST("/retry/:id",
			config.AdminMiddleware.RequireResourcePermission("callback", "write"),
			config.CallbackHandler.RetryCallback)

		// 回调统计 - 需要回调读权限
		adminGroup.GET("/statistics",
			config.AdminMiddleware.RequireResourcePermission("callback", "read"),
			config.CallbackHandler.GetAdminCallbackStatistics)
	}
}

// createUnifiedCallbackHandler 创建统一回调处理器
// 🔥 智能分发：根据回调内容自动判断是物流回调还是工单回调
func createUnifiedCallbackHandler(provider string, config CallbackRouterConfig) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 读取原始请求数据
		rawData, err := c.GetRawData()
		if err != nil {
			c.JSON(200, gin.H{
				"success": false,
				"message": "读取请求数据失败",
			})
			return
		}

		// 获取请求头
		headers := make(map[string]string)
		for key, values := range c.Request.Header {
			if len(values) > 0 {
				headers[key] = values[0]
			}
		}

		// 🔥 智能判断回调类型
		callbackType := determineCallbackType(rawData, headers, provider)

		switch callbackType {
		case "workorder":
			// 工单回调处理
			if config.WorkOrderCallbackForwarder != nil {
				processWorkOrderCallback(c, provider, rawData, headers, config.WorkOrderCallbackForwarder)
			} else {
				c.JSON(200, gin.H{"success": false, "message": "工单回调处理器未配置"})
			}
		case "logistics":
			// 🔥 修复：直接处理物流回调，避免重复读取请求体
			processLogisticsCallback(c, provider, rawData, headers, config)
		default:
			// 🔥 修复：默认使用物流回调处理，避免重复读取请求体
			processLogisticsCallback(c, provider, rawData, headers, config)
		}
	}
}

// processLogisticsCallback 处理物流回调
// 🔥 新增：避免重复读取请求体的物流回调处理器
func processLogisticsCallback(c *gin.Context, provider string, rawData []byte, headers map[string]string, config CallbackRouterConfig) {
	// 直接调用CallbackHandler的内部逻辑，但使用已读取的rawData
	// 这样避免了重复读取请求体的问题

	// 处理回调 - 使用公开的方法访问服务
	response, err := config.CallbackHandler.GetCallbackService().ProcessCallback(c.Request.Context(), provider, rawData, headers)
	if err != nil {
		// 记录错误
		config.CallbackHandler.GetLogger().Error("处理回调失败",
			zap.String("provider", provider),
			zap.Error(err))

		// 返回错误响应，但仍然是200状态码，避免供应商重复推送
		c.JSON(200, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	// 🔥 企业级修复：优化响应头，减少重复推送
	c.Header("Cache-Control", "no-cache, no-store, must-revalidate")
	c.Header("Pragma", "no-cache")
	c.Header("Expires", "0")
	c.Header("Connection", "close")

	// 🔥 企业级修复：根据DirectReturn标记决定响应格式
	if response.DirectReturn {
		// 直接返回Data内容，不包装（易达等供应商要求的格式）
		c.JSON(200, response.Data)
	} else {
		// 返回包装后的响应格式
		c.JSON(200, response)
	}
}

// createWorkOrderCallbackHandler 创建专用工单回调处理器
func createWorkOrderCallbackHandler(provider string, config CallbackRouterConfig) gin.HandlerFunc {
	return func(c *gin.Context) {
		if config.WorkOrderCallbackForwarder == nil {
			c.JSON(200, gin.H{
				"success": false,
				"message": "工单回调处理器未配置",
			})
			return
		}

		// 读取原始请求数据
		rawData, err := c.GetRawData()
		if err != nil {
			c.JSON(200, gin.H{
				"success": false,
				"message": "读取请求数据失败",
			})
			return
		}

		// 获取请求头
		headers := make(map[string]string)
		for key, values := range c.Request.Header {
			if len(values) > 0 {
				headers[key] = values[0]
			}
		}

		processWorkOrderCallback(c, provider, rawData, headers, config.WorkOrderCallbackForwarder)
	}
}

// processWorkOrderCallback 处理工单回调
func processWorkOrderCallback(c *gin.Context, provider string, rawData []byte, headers map[string]string, forwarder *service.WorkOrderCallbackForwarder) {
	// 🔥 快递100特殊处理：将表单数据转换为JSON
	if provider == "kuaidi100" && c.GetHeader("Content-Type") == "application/x-www-form-urlencoded" {
		if convertedData, err := convertCallbackFormToJSON(c, rawData); err == nil {
			rawData = convertedData
		}
	}

	// 处理工单回调
	response, err := forwarder.ProcessCallback(c.Request.Context(), provider, rawData, headers)
	if err != nil {
		// 记录错误但仍返回成功响应，避免供应商重复推送
		c.JSON(200, gin.H{
			"success": true,
			"message": "已接收，处理中",
		})
		return
	}

	// 返回供应商期望的响应格式
	if response.DirectReturn {
		c.JSON(200, response.Data)
	} else {
		c.JSON(200, response)
	}
}

// determineCallbackType 智能判断回调类型
// 🔥 基于回调内容的关键字段判断是物流回调还是工单回调
func determineCallbackType(rawData []byte, headers map[string]string, provider string) string {
	// 转换为字符串进行内容分析
	content := strings.ToLower(string(rawData))

	// 🔥 供应商特定的工单回调识别
	switch provider {
	case "kuaidiniao":
		// 🔥 快递鸟工单回调特征：RequestType=103，CallRequestType=1801，且包含工单标识
		if (strings.Contains(content, "requesttype=103") ||
			strings.Contains(content, "\"requesttype\":\"103\"") ||
			strings.Contains(content, "\"requesttype\": \"103\"") ||
			strings.Contains(content, "callrequesttype=1801") ||
			strings.Contains(content, "\"callrequesttype\":\"1801\"") ||
			strings.Contains(content, "\"callrequesttype\": \"1801\"")) &&
			// 🔥 确保包含工单特有字段：TicketId, TicketSource, State=401
			(strings.Contains(content, "ticketid") ||
				strings.Contains(content, "ticketsource") ||
				strings.Contains(content, "\"state\":\"401\"") ||
				strings.Contains(content, "\"state\": \"401\"")) {
			return "workorder"
		}
		// 🔥 订单取消确认回调：State=203等状态，但不包含工单字段
		if strings.Contains(content, "\"state\":\"203\"") ||
			strings.Contains(content, "\"state\": \"203\"") ||
			strings.Contains(content, "\"confirmedby\":\"active_query\"") {
			return "logistics" // 这是订单状态回调，不是工单回调
		}
	case "yuntong":
		// 云通工单回调特征：RequestType=105，包含ComplaintNumber、ComplaintType
		if strings.Contains(content, "complaintnumber") ||
			strings.Contains(content, "complainttype") ||
			strings.Contains(content, "\"requesttype\":105") ||
			strings.Contains(content, "\"requesttype\": 105") {
			return "workorder"
		}
	case "kuaidi100":
		// 快递100工单回调特征：包含工单ID、状态等
		if strings.Contains(content, "consultid") ||
			strings.Contains(content, "secondtype") ||
			strings.Contains(content, "workorder") {
			return "workorder"
		}
	case "yida":
		// 易达工单回调特征：包含taskNo、工单类型等
		if strings.Contains(content, "taskno") ||
			strings.Contains(content, "task_no") ||
			strings.Contains(content, "workordertype") {
			return "workorder"
		}
	}

	// 🔥 通用工单回调的特征识别
	workorderKeywords := []string{
		"workorderid", "work_order_id",
		"ticketid", "ticket_id",
		"complaintnumber", "complainttype", // 云通工单字段
		"taskno", "task_no", // 易达工单字段
		"consultid", // 快递100工单字段
		"result", "reply", "solution",
		"处理结果", "回复内容",
	}

	// 🔥 物流回调的特征识别
	logisticsKeywords := []string{
		"trackingnumber", "tracking_number", "waybill_no",
		"location", "time",
		"logistics", "express", "delivery",
		"pushtype", "ydorderstatus", "deliverytype", // 易达物流字段
		"state", "operatetype", // 云通物流字段（但不包含工单特征时）
		"运单号", "物流", "快递",
	}

	// 统计工单关键词出现次数
	workorderScore := 0
	for _, keyword := range workorderKeywords {
		if strings.Contains(content, strings.ToLower(keyword)) {
			workorderScore++
		}
	}

	// 统计物流关键词出现次数
	logisticsScore := 0
	for _, keyword := range logisticsKeywords {
		if strings.Contains(content, strings.ToLower(keyword)) {
			logisticsScore++
		}
	}

	// 🔥 基于评分决定回调类型
	if workorderScore > logisticsScore && workorderScore > 0 {
		return "workorder"
	} else if logisticsScore > 0 {
		return "logistics"
	}

	// 🔥 基于URL路径判断（向后兼容）
	if strings.Contains(content, "workorder") || strings.Contains(content, "ticket") {
		return "workorder"
	}

	// 默认为物流回调
	return "logistics"
}

// convertCallbackFormToJSON 将表单数据转换为JSON格式（快递100专用）
func convertCallbackFormToJSON(c *gin.Context, rawData []byte) ([]byte, error) {
	// 重新设置请求体以便解析表单
	c.Request.Body = io.NopCloser(bytes.NewReader(rawData))

	// 解析表单数据
	if err := c.Request.ParseForm(); err != nil {
		return nil, err
	}

	// 构建JSON对象
	formData := make(map[string]interface{})
	for key, values := range c.Request.Form {
		if len(values) == 1 {
			formData[key] = values[0]
		} else {
			formData[key] = values
		}
	}

	// 转换为JSON
	return json.Marshal(formData)
}
