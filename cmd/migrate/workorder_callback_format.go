package main

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"strings"
	"time"

	"github.com/google/uuid"
	_ "github.com/lib/pq"
)

// 🔥 工单回调格式统一迁移工具
// 🔥 版本：4.0
// 🔥 目标：将callback_forward_records中的工单回调迁移到work_order_forward_records

type MigrationTool struct {
	db *sql.DB
}

type CallbackRecord struct {
	ForwardID    string          `json:"forward_id"`
	UserID       string          `json:"user_id"`
	CallbackURL  string          `json:"callback_url"`
	RequestData  json.RawMessage `json:"request_data"`
	ResponseData json.RawMessage `json:"response_data"`
	HTTPStatus   int             `json:"http_status"`
	Status       string          `json:"status"`
	RetryCount   int             `json:"retry_count"`
	ErrorMessage string          `json:"error_message"`
	RequestAt    *time.Time      `json:"request_at"`
	ResponseAt   *time.Time      `json:"response_at"`
	CreatedAt    time.Time       `json:"created_at"`
	EventType    string          `json:"event_type"`
	OrderNo      string          `json:"order_no"`
	TrackingNo   string          `json:"tracking_no"`
}

type MigrationResult struct {
	MigratedCount int `json:"migrated_count"`
	SkippedCount  int `json:"skipped_count"`
	ErrorCount    int `json:"error_count"`
}

func main() {
	// 连接数据库
	db, err := sql.Open("postgres", "***************************************************************************")
	if err != nil {
		log.Fatal("连接数据库失败:", err)
	}
	defer db.Close()

	tool := &MigrationTool{db: db}

	fmt.Println("🔥 工单回调格式统一迁移工具 v4.0")
	fmt.Println(strings.Repeat("=", 50))

	// 1. 分析现有数据
	fmt.Println("1. 分析现有数据...")
	if err := tool.analyzeData(); err != nil {
		log.Fatal("数据分析失败:", err)
	}

	// 2. 创建备份
	fmt.Println("2. 创建数据备份...")
	if err := tool.createBackup(); err != nil {
		log.Fatal("创建备份失败:", err)
	}

	// 3. 执行迁移
	fmt.Println("3. 执行数据迁移...")
	result, err := tool.migrateData()
	if err != nil {
		log.Fatal("数据迁移失败:", err)
	}

	// 4. 验证结果
	fmt.Println("4. 验证迁移结果...")
	if err := tool.validateMigration(); err != nil {
		log.Fatal("验证失败:", err)
	}

	// 5. 输出结果
	fmt.Printf("✅ 迁移完成！\n")
	fmt.Printf("   - 成功迁移: %d 条记录\n", result.MigratedCount)
	fmt.Printf("   - 跳过记录: %d 条记录\n", result.SkippedCount)
	fmt.Printf("   - 错误记录: %d 条记录\n", result.ErrorCount)
	fmt.Println(strings.Repeat("=", 50))
}

func (m *MigrationTool) analyzeData() error {
	// 分析work_order_forward_records
	var workOrderCount int
	err := m.db.QueryRow(`
		SELECT COUNT(*) FROM work_order_forward_records
	`).Scan(&workOrderCount)
	if err != nil {
		return fmt.Errorf("查询work_order_forward_records失败: %w", err)
	}

	// 分析callback_forward_records中的工单回调
	var callbackCount int
	err = m.db.QueryRow(`
		SELECT COUNT(*) 
		FROM callback_forward_records cfr
		JOIN unified_callback_records ucr ON cfr.callback_record_id = ucr.id
		WHERE ucr.event_type LIKE '%workorder%'
	`).Scan(&callbackCount)
	if err != nil {
		return fmt.Errorf("查询callback_forward_records失败: %w", err)
	}

	fmt.Printf("   - work_order_forward_records: %d 条记录\n", workOrderCount)
	fmt.Printf("   - callback_forward_records (工单): %d 条记录\n", callbackCount)

	return nil
}

func (m *MigrationTool) createBackup() error {
	// 创建备份表
	backupSQL := `
		CREATE TABLE IF NOT EXISTS callback_forward_records_backup_20250821 AS 
		SELECT * FROM callback_forward_records WHERE 1=0;
		
		CREATE TABLE IF NOT EXISTS unified_callback_records_backup_20250821 AS 
		SELECT * FROM unified_callback_records WHERE 1=0;
	`

	if _, err := m.db.Exec(backupSQL); err != nil {
		return fmt.Errorf("创建备份表失败: %w", err)
	}

	// 备份数据
	backupDataSQL := `
		INSERT INTO callback_forward_records_backup_20250821
		SELECT cfr.* 
		FROM callback_forward_records cfr
		JOIN unified_callback_records ucr ON cfr.callback_record_id = ucr.id
		WHERE ucr.event_type LIKE '%workorder%';

		INSERT INTO unified_callback_records_backup_20250821
		SELECT ucr.* 
		FROM unified_callback_records ucr
		WHERE ucr.event_type LIKE '%workorder%';
	`

	if _, err := m.db.Exec(backupDataSQL); err != nil {
		return fmt.Errorf("备份数据失败: %w", err)
	}

	fmt.Println("   ✅ 数据备份完成")
	return nil
}

func (m *MigrationTool) migrateData() (*MigrationResult, error) {
	result := &MigrationResult{}

	// 查询需要迁移的记录
	rows, err := m.db.Query(`
		SELECT 
			cfr.id as forward_id,
			cfr.user_id,
			cfr.callback_url,
			cfr.request_data,
			cfr.response_data,
			cfr.http_status,
			cfr.status,
			cfr.retry_count,
			cfr.error_message,
			cfr.request_at,
			cfr.response_at,
			cfr.created_at,
			ucr.event_type,
			ucr.order_no,
			ucr.tracking_no
		FROM callback_forward_records cfr
		JOIN unified_callback_records ucr ON cfr.callback_record_id = ucr.id
		WHERE ucr.event_type LIKE '%workorder%'
	`)
	if err != nil {
		return nil, fmt.Errorf("查询待迁移记录失败: %w", err)
	}
	defer rows.Close()

	for rows.Next() {
		var rec CallbackRecord
		err := rows.Scan(
			&rec.ForwardID,
			&rec.UserID,
			&rec.CallbackURL,
			&rec.RequestData,
			&rec.ResponseData,
			&rec.HTTPStatus,
			&rec.Status,
			&rec.RetryCount,
			&rec.ErrorMessage,
			&rec.RequestAt,
			&rec.ResponseAt,
			&rec.CreatedAt,
			&rec.EventType,
			&rec.OrderNo,
			&rec.TrackingNo,
		)
		if err != nil {
			result.ErrorCount++
			continue
		}

		if err := m.migrateRecord(&rec); err != nil {
			fmt.Printf("   ⚠️ 迁移记录失败: %s, 错误: %v\n", rec.ForwardID, err)
			result.ErrorCount++
		} else {
			result.MigratedCount++
		}
	}

	return result, nil
}

func (m *MigrationTool) migrateRecord(rec *CallbackRecord) error {
	// 查找对应的工单ID
	var workOrderID string
	err := m.db.QueryRow(`
		SELECT id FROM work_orders 
		WHERE order_no = $1 OR tracking_no = $2
		LIMIT 1
	`, rec.OrderNo, rec.TrackingNo).Scan(&workOrderID)
	if err != nil {
		return fmt.Errorf("找不到对应的工单: %w", err)
	}

	// 解析原始请求数据
	var originalData map[string]interface{}
	if err := json.Unmarshal(rec.RequestData, &originalData); err != nil {
		return fmt.Errorf("解析原始请求数据失败: %w", err)
	}

	// 转换事件类型
	eventType := m.convertEventType(rec.EventType)

	// 构建新的请求数据（UnifiedWorkOrderCallbackData格式）
	newRequestData := map[string]interface{}{
		"event_type":             eventType,
		"event_time":             rec.CreatedAt.Unix(),
		"timestamp":              rec.CreatedAt.Format(time.RFC3339),
		"version":                "4.0",
		"work_order_id":          workOrderID,
		"provider_work_order_id": m.getStringFromData(originalData, "data.provider_work_order_id"),
		"work_order_type":        m.getIntFromData(originalData, "data.work_type"),
		"work_order_type_name":   m.getStringFromData(originalData, "data.work_type_name"),
		"order_no":               rec.OrderNo,
		"platform_order_no":      rec.OrderNo,
		"customer_order_no":      m.getStringFromData(originalData, "customer_order_no"),
		"tracking_no":            rec.TrackingNo,
		"provider":               "go-kuaidi",
		"status":                 m.getIntFromData(originalData, "data.status"),
		"status_name":            m.getStringFromData(originalData, "data.status_name"),
		"content":                m.getStringFromData(originalData, "data.content"),
		"committer":              m.getStringFromData(originalData, "data.committer"),
		"attachment_urls":        m.getArrayFromData(originalData, "data.attachments"),
		"created_at":             rec.CreatedAt.Unix(),
		"updated_at":             rec.CreatedAt.Unix(),
	}

	newRequestDataBytes, err := json.Marshal(newRequestData)
	if err != nil {
		return fmt.Errorf("序列化新请求数据失败: %w", err)
	}

	// 插入到work_order_forward_records表
	_, err = m.db.Exec(`
		INSERT INTO work_order_forward_records (
			id, work_order_id, user_id, callback_url, event_type,
			request_data, response_data, http_status, status, retry_count,
			error_message, request_at, response_at, created_at, updated_at
		) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15)
	`,
		uuid.New(),
		workOrderID,
		rec.UserID,
		rec.CallbackURL,
		eventType,
		newRequestDataBytes,
		rec.ResponseData,
		rec.HTTPStatus,
		rec.Status,
		rec.RetryCount,
		rec.ErrorMessage,
		rec.RequestAt,
		rec.ResponseAt,
		rec.CreatedAt,
		rec.CreatedAt,
	)

	return err
}

func (m *MigrationTool) convertEventType(oldEventType string) string {
	switch oldEventType {
	case "workorder_updated":
		return "workorder.updated"
	case "workorder_created":
		return "workorder.created"
	case "workorder_replied":
		return "workorder.replied"
	case "workorder_closed":
		return "workorder.completed"
	default:
		return "workorder.updated"
	}
}

func (m *MigrationTool) getStringFromData(data map[string]interface{}, path string) string {
	// 简单的路径解析，支持 "data.field" 格式
	if path == "customer_order_no" {
		if val, ok := data[path]; ok {
			if str, ok := val.(string); ok {
				return str
			}
		}
		return ""
	}

	// 处理 "data.field" 格式
	if dataObj, ok := data["data"].(map[string]interface{}); ok {
		field := path[5:] // 去掉 "data." 前缀
		if val, ok := dataObj[field]; ok {
			if str, ok := val.(string); ok {
				return str
			}
		}
	}
	return ""
}

func (m *MigrationTool) getIntFromData(data map[string]interface{}, path string) int {
	if dataObj, ok := data["data"].(map[string]interface{}); ok {
		field := path[5:] // 去掉 "data." 前缀
		if val, ok := dataObj[field]; ok {
			if num, ok := val.(float64); ok {
				return int(num)
			}
		}
	}
	return 0
}

func (m *MigrationTool) getArrayFromData(data map[string]interface{}, path string) []interface{} {
	if dataObj, ok := data["data"].(map[string]interface{}); ok {
		field := path[5:] // 去掉 "data." 前缀
		if val, ok := dataObj[field]; ok {
			if arr, ok := val.([]interface{}); ok {
				return arr
			}
		}
	}
	return []interface{}{}
}

func (m *MigrationTool) validateMigration() error {
	// 检查迁移后的数据
	var workOrderCount, callbackCount int

	err := m.db.QueryRow(`SELECT COUNT(*) FROM work_order_forward_records`).Scan(&workOrderCount)
	if err != nil {
		return fmt.Errorf("验证work_order_forward_records失败: %w", err)
	}

	err = m.db.QueryRow(`
		SELECT COUNT(*) 
		FROM callback_forward_records cfr
		JOIN unified_callback_records ucr ON cfr.callback_record_id = ucr.id
		WHERE ucr.event_type LIKE '%workorder%'
	`).Scan(&callbackCount)
	if err != nil {
		return fmt.Errorf("验证callback_forward_records失败: %w", err)
	}

	fmt.Printf("   - work_order_forward_records: %d 条记录\n", workOrderCount)
	fmt.Printf("   - callback_forward_records (工单剩余): %d 条记录\n", callbackCount)

	return nil
}
