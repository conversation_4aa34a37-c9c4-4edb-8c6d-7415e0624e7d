<template>
  <div class="workorder-list-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>售后服务</h2>
      <p class="subtitle">查看和管理您的售后服务申请</p>
    </div>

    <!-- 搜索和筛选区域 -->
    <div class="search-section">
      <el-card>
        <el-form :model="searchForm" inline>
          <el-form-item label="处理状态">
            <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
              <el-option
                v-for="(name, value) in unifiedStatusOptions"
                :key="value"
                :label="name"
                :value="Number(value)"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="服务类型">
            <el-select v-model="searchForm.work_order_type" placeholder="请选择类型" clearable>
              <el-option
                v-for="type in userFriendlyTypes"
                :key="type.value"
                :label="type.label"
                :value="type.value"
              />
            </el-select>
          </el-form-item>

          <!-- 🔥 移除供应商筛选，用户不需要关心供应商 -->

          <el-form-item label="订单号">
            <el-input
              v-model="searchForm.order_no"
              placeholder="请输入订单号"
              clearable
            />
          </el-form-item>

          <el-form-item label="运单号">
            <el-input
              v-model="searchForm.tracking_no"
              placeholder="请输入运单号"
              clearable
            />
          </el-form-item>

          <el-form-item label="创建时间">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              @change="handleDateRangeChange"
            />
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="handleReset">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 操作按钮区域 -->
    <div class="action-section">
      <el-button type="primary" @click="handleCreate">
        <el-icon><Plus /></el-icon>
        申请售后服务
      </el-button>
      <el-button @click="handleRefresh">
        <el-icon><Refresh /></el-icon>
        刷新
      </el-button>
    </div>

    <!-- 工单列表 -->
    <div class="table-section">
      <el-card>
        <el-table
          v-loading="loading"
          :data="workOrderList"
          stripe
          @row-click="handleRowClick"
        >
          <el-table-column prop="title" label="服务申请" min-width="200" show-overflow-tooltip />

          <el-table-column prop="work_order_type" label="服务类型" width="120">
            <template #default="{ row }">
              <el-tag size="small">{{ getTypeName(row.work_order_type) }}</el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="status" label="处理状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getUnifiedStatusType(row.status)" size="small">
                {{ getUnifiedStatusName(row.status) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="priority" label="紧急程度" width="80">
            <template #default="{ row }">
              <el-tag :type="getPriorityType(row.priority)" size="small">
                {{ getPriorityName(row.priority) }}
              </el-tag>
            </template>
          </el-table-column>

          <!-- 🔥 移除供应商列，用户不需要看到供应商信息 -->

          <el-table-column prop="order_no" label="关联订单" width="150" show-overflow-tooltip />
          <el-table-column prop="tracking_no" label="运单号" width="150" show-overflow-tooltip />

          <el-table-column prop="created_at" label="创建时间" width="160">
            <template #default="{ row }">
              {{ formatDateTime(row.created_at) }}
            </template>
          </el-table-column>

          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button type="primary" size="small" @click.stop="handleView(row)">
                查看
              </el-button>
              <el-button
                v-if="canReply(row)"
                type="success"
                size="small"
                @click.stop="handleReply(row)"
              >
                回复
              </el-button>
              <el-button
                v-if="canDelete(row).canDelete"
                type="danger"
                size="small"
                @click.stop="handleDelete(row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-section">
          <el-pagination
            v-model:current-page="pagination.page"
            v-model:page-size="pagination.page_size"
            :total="pagination.total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>

    <!-- 工单详情对话框 -->
    <WorkOrderDetailDialog
      v-model:visible="showDetailDialog"
      :work-order-id="selectedWorkOrderId"
      @refresh="handleRefresh"
    />

    <!-- 创建工单对话框 -->
    <CreateWorkOrderDialog
      v-model:visible="showCreateDialog"
      @success="handleCreateSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh, Plus } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import { WorkOrderService } from '@/api'
import type {
  WorkOrder,
  WorkOrderListRequest,
  WorkOrderStatus,
  WorkOrderType,
  WorkOrderPriority
} from '@/api/model/workOrderModel'
import { WorkOrderStatusNames, WorkOrderTypeNames, WorkOrderPriorityNames } from '@/api/model/workOrderModel'
import { ErrorHandler } from '@/utils/errorHandler'
import WorkOrderDetailDialog from './components/WorkOrderDetailDialog.vue'
import CreateWorkOrderDialog from './components/CreateWorkOrderDialog.vue'
// 🔥 新增：使用统一的工单类型管理
import { useWorkOrderTypes } from '@/composables/useWorkOrderTypes'

// 路由
const router = useRouter()

// 🔥 使用统一的工单类型管理
const {
  userFriendlyTypes,
  fetchWorkOrderTypes,
  getTypeName
} = useWorkOrderTypes()

// 响应式数据
const loading = ref(false)
const showDetailDialog = ref(false)
const showCreateDialog = ref(false)
const selectedWorkOrderId = ref('')
const workOrderList = ref<WorkOrder[]>([])
const dateRange = ref<[Date, Date] | undefined>(undefined)

// 搜索表单
const searchForm = reactive<WorkOrderListRequest>({
  page: 1,
  page_size: 20,
  status: undefined,
  work_order_type: undefined,
  provider: undefined,
  order_no: '',
  tracking_no: '',
  start_date: undefined,
  end_date: undefined
})

// 分页数据
const pagination = reactive({
  page: 1,
  page_size: 20,
  total: 0
})

// 🔥 统一的状态选项（隐藏供应商差异）
const unifiedStatusOptions = computed(() => ({
  1: '待处理',
  2: '处理中',
  3: '已完结'
}))

// 计算属性（保留原有的作为备用）
const statusOptions = computed(() => WorkOrderStatusNames)
const typeOptions = computed(() => WorkOrderTypeNames)

// 方法
const getWorkOrderList = async () => {
  loading.value = true
  try {
    const params = {
      ...searchForm,
      page: pagination.page,
      page_size: pagination.page_size
    }

    const response = await WorkOrderService.getWorkOrderList(params)

    if (response.success && response.data) {
      workOrderList.value = response.data.items || []
      pagination.total = response.data.total || 0
    } else {
      throw new Error(response.message || '获取工单列表失败')
    }
  } catch (error) {
    ErrorHandler.handleApiError(error)
    workOrderList.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.page = 1
  getWorkOrderList()
}

const handleReset = () => {
  Object.assign(searchForm, {
    page: 1,
    page_size: 20,
    status: undefined,
    work_order_type: undefined,
    provider: undefined,
    order_no: '',
    tracking_no: '',
    start_date: undefined,
    end_date: undefined
  })
  dateRange.value = undefined
  pagination.page = 1
  getWorkOrderList()
}

const handleRefresh = () => {
  getWorkOrderList()
}

const handleCreate = () => {
  showCreateDialog.value = true
}

const handleView = (row: WorkOrder) => {
  selectedWorkOrderId.value = row.id
  showDetailDialog.value = true
}

const handleReply = (row: WorkOrder) => {
  selectedWorkOrderId.value = row.id
  showDetailDialog.value = true
}

const handleRowClick = (row: WorkOrder) => {
  handleView(row)
}

const handleDateRangeChange = (dates: [Date, Date] | undefined) => {
  if (dates) {
    searchForm.start_date = dates[0].toISOString().split('T')[0]
    searchForm.end_date = dates[1].toISOString().split('T')[0]
  } else {
    searchForm.start_date = undefined
    searchForm.end_date = undefined
  }
}

const handleSizeChange = (size: number) => {
  pagination.page_size = size
  pagination.page = 1
  getWorkOrderList()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  getWorkOrderList()
}

const handleCreateSuccess = () => {
  showCreateDialog.value = false
  handleRefresh()
  ElMessage.success('售后申请提交成功')
}

const handleDelete = async (row: WorkOrder) => {
  // 检查是否可以删除
  const deleteCheck = WorkOrderService.canDeleteWorkOrder(row)
  if (!deleteCheck.canDelete) {
    ElMessage.error(deleteCheck.reason || '无法删除该工单')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除工单"${row.title}"吗？删除后无法恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger'
      }
    )

    // 执行删除
    loading.value = true
    const response = await WorkOrderService.deleteWorkOrder(row.id)

    if (response.success) {
      ElMessage.success('工单删除成功')
      handleRefresh()
    } else {
      throw new Error(response.message || '删除失败')
    }
  } catch (error: any) {
    if (error !== 'cancel') { // 用户取消操作不显示错误
      ErrorHandler.handleApiError(error)
    }
  } finally {
    loading.value = false
  }
}

// 🔥 统一的工具方法（隐藏供应商差异）
const getUnifiedStatusName = (status: WorkOrderStatus) => {
  const statusMap: Record<number, string> = {
    1: '待处理',
    2: '处理中',
    3: '已完结'
  }
  return statusMap[status] || '未知状态'
}

// Element Plus Tag 类型定义
type TagType = 'primary' | 'success' | 'warning' | 'info' | 'danger'

const getUnifiedStatusType = (status: WorkOrderStatus): TagType => {
  const typeMap: Record<number, TagType> = {
    1: 'warning',   // 待处理
    2: 'primary',   // 处理中
    3: 'success'    // 已完结
  }
  return typeMap[status] || 'info'
}

// 🔥 移除硬编码的类型映射，使用动态获取的类型名称
// getUnifiedTypeName 方法已被 getTypeName 替代

// 工具方法（保留原有的作为备用）
const getStatusName = (status: WorkOrderStatus) => {
  return WorkOrderService.formatWorkOrderStatus(status).text
}

const getStatusType = (status: WorkOrderStatus): TagType => {
  const result = WorkOrderService.formatWorkOrderStatus(status).type
  const validColors: TagType[] = ['primary', 'success', 'warning', 'info', 'danger']
  return validColors.includes(result as TagType) ? result as TagType : 'info'
}

// getTypeName 已通过 useWorkOrderTypes() 组合函数导入，无需重复定义

const getPriorityName = (priority: WorkOrderPriority) => {
  return WorkOrderService.formatWorkOrderPriority(priority).text
}

const getPriorityType = (priority: WorkOrderPriority): TagType => {
  const result = WorkOrderService.formatWorkOrderPriority(priority).type
  const validColors: TagType[] = ['primary', 'success', 'warning', 'info', 'danger']
  return validColors.includes(result as TagType) ? result as TagType : 'info'
}

// 🔥 移除供应商名称映射，用户不需要看到供应商信息

const canReply = (workOrder: WorkOrder) => {
  return WorkOrderService.canReplyWorkOrder(workOrder)
}

const canDelete = (workOrder: WorkOrder) => {
  return WorkOrderService.canDeleteWorkOrder(workOrder)
}

const formatDateTime = (dateTime: string) => {
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 生命周期
onMounted(async () => {
  // 🔥 先获取工单类型，再获取工单列表
  // 获取所有供应商的工单类型
  await fetchWorkOrderTypes()
  getWorkOrderList()
})
</script>

<style scoped lang="scss">
.workorder-list-container {
  padding: 20px;

  .page-header {
    margin-bottom: 20px;
    
    h2 {
      margin: 0 0 8px 0;
      color: #303133;
      font-size: 24px;
      font-weight: 600;
    }
    
    .subtitle {
      margin: 0;
      color: #909399;
      font-size: 14px;
    }
  }

  .search-section {
    margin-bottom: 20px;
  }

  .action-section {
    margin-bottom: 20px;
    display: flex;
    gap: 12px;
  }

  .table-section {
    .pagination-section {
      margin-top: 20px;
      display: flex;
      justify-content: center;
    }
  }
}

:deep(.el-table__row) {
  cursor: pointer;

  &:hover {
    background-color: #f5f7fa;
  }
}

// 移动端适配
@media (max-width: 768px) {
  .workorder-list-container {
    padding: 12px;

    .search-section {
      :deep(.el-form--inline) {
        .el-form-item {
          display: block;
          margin-right: 0;
          margin-bottom: 12px;

          .el-form-item__content {
            margin-left: 0 !important;
          }
        }
      }
    }

    .action-section {
      flex-direction: column;
      gap: 8px;

      .el-button {
        width: 100%;
      }
    }

    .table-section {
      :deep(.el-table) {
        .el-table__header-wrapper,
        .el-table__body-wrapper {
          overflow-x: auto;
        }

        // 隐藏部分列在移动端
        .el-table__cell:nth-child(n+6) {
          display: none;
        }
      }

      .pagination-section {
        :deep(.el-pagination) {
          justify-content: center;

          .el-pagination__sizes,
          .el-pagination__jump {
            display: none;
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .workorder-list-container {
    padding: 8px;

    .page-header {
      text-align: center;

      h2 {
        font-size: 20px;
      }
    }

    .table-section {
      :deep(.el-table) {
        // 在小屏幕上只显示关键列
        .el-table__cell:nth-child(n+4) {
          display: none;
        }
      }
    }
  }
}
</style>
