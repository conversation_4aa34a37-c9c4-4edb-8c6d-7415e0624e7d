#!/bin/bash

# 测试工单ID搜索功能
# 使用管理员令牌进行测试

ADMIN_TOKEN_FILE="admin-token.txt"
BASE_URL="http://localhost:8081"

# 检查令牌文件是否存在
if [ ! -f "$ADMIN_TOKEN_FILE" ]; then
    echo "❌ 管理员令牌文件不存在: $ADMIN_TOKEN_FILE"
    exit 1
fi

# 读取管理员令牌
ADMIN_TOKEN=$(cat "$ADMIN_TOKEN_FILE")

if [ -z "$ADMIN_TOKEN" ]; then
    echo "❌ 管理员令牌为空"
    exit 1
fi

echo "🧪 开始测试工单ID搜索功能"
echo "🔑 使用管理员令牌: ${ADMIN_TOKEN:0:20}..."
echo ""

# 测试1: 获取原始回调数据列表（不带工单ID筛选）
echo "📋 测试1: 获取原始回调数据列表（不带工单ID筛选）"
curl -s -X GET "$BASE_URL/api/v1/admin/raw-callbacks/records?page=1&page_size=5" \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -H "Content-Type: application/json" | jq '.'

echo -e "\n" 

# 测试2: 使用工单ID搜索（假设工单ID）
echo "📋 测试2: 使用工单ID搜索"
curl -s -X GET "$BASE_URL/api/v1/admin/raw-callbacks/records?page=1&page_size=5&work_order_id=5633415" \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -H "Content-Type: application/json" | jq '.'

echo -e "\n"

# 测试3: 使用部分工单ID搜索
echo "📋 测试3: 使用部分工单ID搜索"  
curl -s -X GET "$BASE_URL/api/v1/admin/raw-callbacks/records?page=1&page_size=5&work_order_id=5633" \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -H "Content-Type: application/json" | jq '.'

echo -e "\n"

# 测试4: 组合搜索 - 工单ID + 供应商
echo "📋 测试4: 组合搜索（工单ID + 供应商）"
curl -s -X GET "$BASE_URL/api/v1/admin/raw-callbacks/records?page=1&page_size=5&work_order_id=5633&provider=kuaidi100" \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -H "Content-Type: application/json" | jq '.'

echo -e "\n"

# 测试5: 导出功能测试（包含工单ID参数）
echo "📋 测试5: 导出功能测试（包含工单ID参数）"
curl -s -X GET "$BASE_URL/api/v1/admin/raw-callbacks/export?work_order_id=5633&provider=kuaidi100" \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -o "test_export_with_workorder_id.csv"

if [ -f "test_export_with_workorder_id.csv" ]; then
    echo "✅ 导出文件已生成: test_export_with_workorder_id.csv"
    echo "📄 文件大小: $(wc -c < test_export_with_workorder_id.csv) bytes"
    echo "📄 前几行内容:"
    head -3 test_export_with_workorder_id.csv
else
    echo "❌ 导出文件生成失败"
fi

echo -e "\n🎉 工单ID搜索功能测试完成！"