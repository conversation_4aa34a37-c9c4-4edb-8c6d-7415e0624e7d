package service

import (
	"context"
	"encoding/json"
	"fmt"
	"net/url"
	"strings"
	"time"

	"go.uber.org/zap"

	"callback-receiver/internal/model"
)

// WorkOrderCallbackData 工单回调数据
type WorkOrderCallbackData struct {
	ProviderWorkOrderID string                 `json:"provider_work_order_id"`
	Status              int                    `json:"status"`
	Content             string                 `json:"content"`
	Committer           string                 `json:"committer"`
	AttachmentURLs      []string               `json:"attachment_urls"`
	RawData             map[string]interface{} `json:"raw_data"`
	Timestamp           time.Time              `json:"timestamp"`
}

// WorkOrderCallbackService 工单回调服务
type WorkOrderCallbackService struct {
	logger *zap.Logger
}

// NewWorkOrderCallbackService 创建工单回调服务
func NewWorkOrderCallbackService(logger *zap.Logger) *WorkOrderCallbackService {
	return &WorkOrderCallbackService{
		logger: logger,
	}
}

// ProcessWorkOrderCallback 处理工单回调
func (s *WorkOrderCallbackService) ProcessWorkOrderCallback(ctx context.Context, provider string, rawData []byte, headers map[string]string) (*model.CallbackResponse, error) {
	s.logger.Info("🔄 开始处理工单回调",
		zap.String("provider", provider),
		zap.Int("data_size", len(rawData)),
		zap.Any("headers", headers))

	// 1. 解析回调数据
	callbackData, err := s.parseWorkOrderCallback(provider, rawData)
	if err != nil {
		s.logger.Error("❌ 解析工单回调数据失败",
			zap.String("provider", provider),
			zap.Error(err))
		return &model.CallbackResponse{
			Success: false,
			Message: fmt.Sprintf("解析回调数据失败: %v", err),
		}, err
	}

	// 2. 验证回调数据
	if err := s.validateWorkOrderCallback(callbackData); err != nil {
		s.logger.Error("❌ 工单回调数据验证失败",
			zap.String("provider", provider),
			zap.String("provider_work_order_id", callbackData.ProviderWorkOrderID),
			zap.Error(err))
		return &model.CallbackResponse{
			Success: false,
			Message: fmt.Sprintf("回调数据验证失败: %v", err),
		}, err
	}

	// 3. 记录回调处理日志
	s.logger.Info("✅ 工单回调处理成功",
		zap.String("provider", provider),
		zap.String("provider_work_order_id", callbackData.ProviderWorkOrderID),
		zap.Int("status", callbackData.Status),
		zap.String("content", callbackData.Content))

	return &model.CallbackResponse{
		Success: true,
		Message: "工单回调处理成功",
	}, nil
}

// parseWorkOrderCallback 解析工单回调数据
func (s *WorkOrderCallbackService) parseWorkOrderCallback(provider string, rawData []byte) (*WorkOrderCallbackData, error) {
	var callbackData WorkOrderCallbackData
	callbackData.Timestamp = time.Now()

	switch provider {
	case "kuaidiniao":
		return s.parseKuaidiniaoWorkOrderCallback(rawData)
	case "kuaidi100":
		return s.parseKuaidi100WorkOrderCallback(rawData)
	case "yida":
		return s.parseYidaWorkOrderCallback(rawData)
	case "yuntong":
		return s.parseYuntongWorkOrderCallback(rawData)
	default:
		// 通用JSON解析
		var rawMap map[string]interface{}
		if err := json.Unmarshal(rawData, &rawMap); err != nil {
			return nil, fmt.Errorf("解析JSON数据失败: %w", err)
		}
		callbackData.RawData = rawMap

		// 尝试提取通用字段
		if workOrderID, ok := rawMap["workorderId"].(string); ok {
			callbackData.ProviderWorkOrderID = workOrderID
		}
		if status, ok := rawMap["status"].(float64); ok {
			callbackData.Status = int(status)
		}
		if content, ok := rawMap["content"].(string); ok {
			callbackData.Content = content
		}
	}

	return &callbackData, nil
}

// parseKuaidiniaoWorkOrderCallback 解析快递鸟工单回调
func (s *WorkOrderCallbackService) parseKuaidiniaoWorkOrderCallback(rawData []byte) (*WorkOrderCallbackData, error) {
	// 🔥 快递鸟工单回调数据是URL编码的表单数据，需要先解析表单
	dataStr := string(rawData)

	var callbackData *WorkOrderCallbackData
	var err error

	if strings.Contains(dataStr, "RequestData=") {
		// URL编码的表单数据格式（快递鸟官方格式）
		callbackData, err = s.parseKuaidiniaoFormData(dataStr)
		if err != nil {
			return nil, fmt.Errorf("解析快递鸟工单表单数据失败: %w", err)
		}
	} else {
		// JSON格式（测试数据或其他格式）
		var rawMap map[string]interface{}
		if err := json.Unmarshal(rawData, &rawMap); err != nil {
			return nil, fmt.Errorf("解析快递鸟工单JSON数据失败: %w", err)
		}

		callbackData = &WorkOrderCallbackData{
			RawData:   rawMap,
			Timestamp: time.Now(),
		}

		// 提取快递鸟工单字段
		if ticketNumber, ok := rawMap["TicketNumber"].(string); ok {
			callbackData.ProviderWorkOrderID = ticketNumber
		}
		if status, ok := rawMap["Status"].(float64); ok {
			callbackData.Status = int(status)
		}
		if dealResult, ok := rawMap["DealResult"].(string); ok {
			callbackData.Content = dealResult
		}
	}

	return callbackData, nil
}

// parseKuaidiniaoFormData 解析快递鸟工单URL编码的表单数据
func (s *WorkOrderCallbackService) parseKuaidiniaoFormData(dataStr string) (*WorkOrderCallbackData, error) {
	// 解析URL编码的表单数据
	values, err := url.ParseQuery(dataStr)
	if err != nil {
		return nil, fmt.Errorf("解析URL编码数据失败: %w", err)
	}

	// 提取RequestData字段
	requestData := values.Get("RequestData")
	if requestData == "" {
		return nil, fmt.Errorf("缺少RequestData字段")
	}

	// 解析RequestData中的JSON数据
	var jsonData map[string]interface{}
	if err := json.Unmarshal([]byte(requestData), &jsonData); err != nil {
		return nil, fmt.Errorf("解析RequestData JSON失败: %w", err)
	}

	// 添加其他表单字段
	jsonData["RequestType"] = values.Get("RequestType")
	jsonData["DataSign"] = values.Get("DataSign")

	callbackData := &WorkOrderCallbackData{
		RawData:   jsonData,
		Timestamp: time.Now(),
	}

	// 🔥 验证是否为工单回调（RequestType=103）
	requestType, _ := jsonData["RequestType"].(string)
	if requestType != "103" {
		return nil, fmt.Errorf("非快递鸟工单回调数据: RequestType=%s", requestType)
	}

	// 🔥 从Data数组中提取工单处理结果（根据快递鸟官方文档5.18）
	if dataArray, ok := jsonData["Data"].([]interface{}); ok && len(dataArray) > 0 {
		if workOrderData, ok := dataArray[0].(map[string]interface{}); ok {
			// 提取工单号（TicketId - 官方文档字段名）
			if ticketId, ok := workOrderData["TicketId"].(string); ok {
				callbackData.ProviderWorkOrderID = ticketId
			} else if ticketNumber, ok := workOrderData["TicketNumber"].(string); ok {
				// 兼容旧字段名
				callbackData.ProviderWorkOrderID = ticketNumber
			}

			// 提取处理结果内容（Reason - 官方文档字段名）
			if reason, ok := workOrderData["Reason"].(string); ok {
				callbackData.Content = reason
			} else if dealResult, ok := workOrderData["DealResult"].(string); ok {
				// 兼容旧字段名
				callbackData.Content = dealResult
			}

			// 🔥 修复：严格按照原始回调数据提取status，不使用任何硬编码
			var hasStatus bool

			// 优先从Status字段提取状态
			if statusValue, ok := workOrderData["Status"]; ok {
				if statusFloat, ok := statusValue.(float64); ok {
					callbackData.Status = int(statusFloat)
					hasStatus = true
				} else if statusInt, ok := statusValue.(int); ok {
					callbackData.Status = statusInt
					hasStatus = true
				}
			}

			// 如果没有Status字段，记录错误并返回
			if !hasStatus {
				s.logger.Error("快递鸟工单回调缺少Status字段",
					zap.Any("callback_data", workOrderData))
				return nil, fmt.Errorf("快递鸟工单回调缺少Status字段")
			}

			// 设置处理人（基于OperateType）
			if operateType, ok := workOrderData["OperateType"].(float64); ok {
				if operateType == 1 {
					callbackData.Committer = "快递鸟客服"
				} else if operateType == 2 {
					callbackData.Committer = "物流公司客服"
				} else {
					callbackData.Committer = "客服"
				}
			} else {
				callbackData.Committer = "快递鸟客服" // 默认处理人
			}

			// 提取附件信息
			var attachments []string
			if dealResultFiles, ok := workOrderData["DealResultFiles"].(string); ok && dealResultFiles != "" {
				// 处理结果附件，多个以逗号分隔
				attachments = append(attachments, strings.Split(dealResultFiles, ",")...)
			}
			if ticketPic, ok := workOrderData["TicketPic"].(string); ok && ticketPic != "" {
				// 工单提交附件，多个以逗号分隔
				attachments = append(attachments, strings.Split(ticketPic, ",")...)
			}
			callbackData.AttachmentURLs = attachments
		}
	}

	return callbackData, nil
}

// parseKuaidi100WorkOrderCallback 解析快递100工单回调
func (s *WorkOrderCallbackService) parseKuaidi100WorkOrderCallback(rawData []byte) (*WorkOrderCallbackData, error) {
	var rawMap map[string]interface{}
	if err := json.Unmarshal(rawData, &rawMap); err != nil {
		return nil, fmt.Errorf("解析快递100工单回调数据失败: %w", err)
	}

	callbackData := &WorkOrderCallbackData{
		RawData:   rawMap,
		Timestamp: time.Now(),
	}

	// 提取快递100特有字段
	if workOrderID, ok := rawMap["workorderId"]; ok {
		if id, ok := workOrderID.(string); ok {
			callbackData.ProviderWorkOrderID = id
		} else if id, ok := workOrderID.(float64); ok {
			callbackData.ProviderWorkOrderID = fmt.Sprintf("%.0f", id)
		}
	}

	if status, ok := rawMap["status"].(float64); ok {
		callbackData.Status = int(status)
	}

	if content, ok := rawMap["content"].(string); ok {
		callbackData.Content = content
	}

	if committer, ok := rawMap["committer"].(string); ok {
		callbackData.Committer = committer
	}

	// 处理附件
	if attachments, ok := rawMap["attach"].([]interface{}); ok {
		for _, attachment := range attachments {
			if attachMap, ok := attachment.(map[string]interface{}); ok {
				if url, ok := attachMap["url"].(string); ok {
					callbackData.AttachmentURLs = append(callbackData.AttachmentURLs, url)
				}
			}
		}
	}

	return callbackData, nil
}

// parseYidaWorkOrderCallback 解析易达工单回调
func (s *WorkOrderCallbackService) parseYidaWorkOrderCallback(rawData []byte) (*WorkOrderCallbackData, error) {
	var rawMap map[string]interface{}
	if err := json.Unmarshal(rawData, &rawMap); err != nil {
		return nil, fmt.Errorf("解析易达工单回调数据失败: %w", err)
	}

	callbackData := &WorkOrderCallbackData{
		RawData:   rawMap,
		Timestamp: time.Now(),
	}

	// 提取易达特有字段
	if workOrderID, ok := rawMap["workOrderId"].(string); ok {
		callbackData.ProviderWorkOrderID = workOrderID
	}

	if status, ok := rawMap["status"].(float64); ok {
		callbackData.Status = int(status)
	}

	if content, ok := rawMap["reply"].(string); ok {
		callbackData.Content = content
	}

	if committer, ok := rawMap["operator"].(string); ok {
		callbackData.Committer = committer
	}

	return callbackData, nil
}

// parseYuntongWorkOrderCallback 解析云通工单回调
func (s *WorkOrderCallbackService) parseYuntongWorkOrderCallback(rawData []byte) (*WorkOrderCallbackData, error) {
	var rawMap map[string]interface{}
	if err := json.Unmarshal(rawData, &rawMap); err != nil {
		return nil, fmt.Errorf("解析云通工单回调数据失败: %w", err)
	}

	callbackData := &WorkOrderCallbackData{
		RawData:   rawMap,
		Timestamp: time.Now(),
	}

	// 提取云通特有字段
	if workOrderID, ok := rawMap["WorkOrderId"].(string); ok {
		callbackData.ProviderWorkOrderID = workOrderID
	}

	if status, ok := rawMap["Status"].(float64); ok {
		callbackData.Status = int(status)
	}

	if content, ok := rawMap["Content"].(string); ok {
		callbackData.Content = content
	}

	if committer, ok := rawMap["Operator"].(string); ok {
		callbackData.Committer = committer
	}

	return callbackData, nil
}

// validateWorkOrderCallback 验证工单回调数据
func (s *WorkOrderCallbackService) validateWorkOrderCallback(data *WorkOrderCallbackData) error {
	if data.ProviderWorkOrderID == "" {
		return fmt.Errorf("供应商工单ID不能为空")
	}

	if data.Status <= 0 {
		return fmt.Errorf("工单状态无效: %d", data.Status)
	}

	return nil
}

// ExtractOrderInfo 提取订单信息用于日志
func (s *WorkOrderCallbackService) ExtractOrderInfo(data *model.CallbackRawData) string {
	if data == nil || data.RawBody == "" {
		return "无数据"
	}

	var rawMap map[string]interface{}
	if err := json.Unmarshal([]byte(data.RawBody), &rawMap); err != nil {
		return "解析失败"
	}

	// 尝试提取关键信息
	var info []string

	// 工单ID
	if workOrderID, ok := rawMap["workorderId"]; ok {
		info = append(info, fmt.Sprintf("工单ID:%v", workOrderID))
	} else if workOrderID, ok := rawMap["workOrderId"]; ok {
		info = append(info, fmt.Sprintf("工单ID:%v", workOrderID))
	} else if workOrderID, ok := rawMap["WorkOrderId"]; ok {
		info = append(info, fmt.Sprintf("工单ID:%v", workOrderID))
	}

	// 状态
	if status, ok := rawMap["status"]; ok {
		info = append(info, fmt.Sprintf("状态:%v", status))
	} else if status, ok := rawMap["Status"]; ok {
		info = append(info, fmt.Sprintf("状态:%v", status))
	}

	// 运单号
	if trackingNo, ok := rawMap["trackingNo"]; ok {
		info = append(info, fmt.Sprintf("运单:%v", trackingNo))
	} else if trackingNo, ok := rawMap["TrackingNo"]; ok {
		info = append(info, fmt.Sprintf("运单:%v", trackingNo))
	}

	if len(info) == 0 {
		return "未知工单"
	}

	return fmt.Sprintf("[%s]", fmt.Sprintf("%v", info))
}
