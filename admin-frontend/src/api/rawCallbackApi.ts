import { http } from '@/utils/http'

/**
 * 原始回调管理API服务
 */

// 原始回调记录数据类型定义
export interface RawCallbackRecord {
  id: string
  provider: string
  raw_body: string
  parsed_data?: any
  event_type?: string
  order_no?: string
  tracking_no?: string
  work_order_id?: string  // 🔥 新增：工单ID字段
  processed_at?: string
  process_error?: string
  received_at: string
  created_at: string
}

// 原始回调统计数据类型
export interface RawCallbackStatistics {
  total_records: number
  success_records: number
  failed_records: number
  pending_records: number
  success_rate: number
  provider_stats: {
    [provider: string]: {
      total: number
      success: number
      failed: number
      pending: number
    }
  }
  event_type_stats: {
    [event_type: string]: {
      total: number
      success: number
      failed: number
      pending: number
    }
  }
  daily_stats: {
    date: string
    total: number
    success: number
    failed: number
    pending: number
  }[]
}

// 原始回调记录查询参数
export interface RawCallbackListParams {
  page?: number
  page_size?: number
  provider?: string
  event_type?: string
  order_no?: string
  tracking_no?: string
  work_order_id?: string  // 🔥 新增：工单ID查询参数
  start_time?: string
  end_time?: string
  status?: string
}

// 原始回调记录列表响应
export interface RawCallbackListResult {
  records: RawCallbackRecord[]
  total: number
  page: number
  page_size: number
}

// 原始回调统计查询参数
export interface RawCallbackStatisticsParams {
  start_time?: string
  end_time?: string
  provider?: string
  event_type?: string
}

// 批量重推请求
export interface BatchRetryRequest {
  record_ids: string[]
}

// 按条件批量重推请求
export interface BatchRetryByConditionRequest {
  provider?: string
  event_type?: string
  start_time?: string
  end_time?: string
  status?: string
}

// 批量重推响应
export interface BatchRetryResult {
  total: number
  success_count: number
  failed_count: number
  errors: string[]
}

/**
 * 原始回调管理API服务
 */
export class RawCallbackManagementService {
  private static readonly BASE_URL = '/api/v1/admin/raw-callbacks'

  /**
   * 获取原始回调记录列表
   */
  static async getRawCallbackRecords(params: RawCallbackListParams = {}): Promise<RawCallbackListResult> {
    try {
      const response = await http.get<{ success: boolean, data: RawCallbackListResult }>({
        url: `${this.BASE_URL}/records`,
        params: {
          page: params.page || 1,
          page_size: params.page_size || 50,
          provider: params.provider || '',
          event_type: params.event_type || '',
          order_no: params.order_no || '',
          tracking_no: params.tracking_no || '',
          work_order_id: params.work_order_id || '',  // 🔥 新增：工单ID参数
          start_time: params.start_time || '',
          end_time: params.end_time || '',
          status: params.status || '',
          _t: Date.now() // 添加时间戳防止缓存
        }
      })

      if (response && response.success && response.data) {
        return response.data
      }

      throw new Error('获取原始回调记录失败')
    } catch (error: any) {
      console.error('Get raw callback records failed:', error)
      throw new Error(error.message || '获取原始回调记录失败')
    }
  }

  /**
   * 获取原始回调记录详情
   */
  static async getRawCallbackRecordById(recordId: string): Promise<RawCallbackRecord> {
    try {
      const response = await http.get<{ success: boolean, data: RawCallbackRecord }>({
        url: `${this.BASE_URL}/records/${recordId}`
      })

      if (response && response.success && response.data) {
        return response.data
      }

      throw new Error('获取原始回调记录详情失败')
    } catch (error: any) {
      console.error('Get raw callback record by id failed:', error)
      throw new Error(error.message || '获取原始回调记录详情失败')
    }
  }

  /**
   * 重推单个原始回调
   */
  static async retryRawCallback(recordId: string): Promise<void> {
    try {
      const response = await http.post<{ success: boolean, message?: string }>({
        url: `${this.BASE_URL}/retry/${recordId}`
      })

      if (response && !response.success) {
        throw new Error(response.message || '重推原始回调失败')
      }
    } catch (error: any) {
      console.error('Retry raw callback failed:', error)
      throw new Error(error.message || '重推原始回调失败')
    }
  }

  /**
   * 批量重推原始回调
   */
  static async batchRetryRawCallbacks(recordIds: string[]): Promise<BatchRetryResult> {
    try {
      const response = await http.post<{ success: boolean, data: BatchRetryResult, message?: string }>({
        url: `${this.BASE_URL}/batch-retry`,
        data: { record_ids: recordIds }
      })

      if (response && response.success && response.data) {
        return response.data
      }

      throw new Error(response?.message || '批量重推原始回调失败')
    } catch (error: any) {
      console.error('Batch retry raw callbacks failed:', error)
      throw new Error(error.message || '批量重推原始回调失败')
    }
  }

  /**
   * 按条件批量重推原始回调
   */
  static async batchRetryByCondition(conditions: BatchRetryByConditionRequest): Promise<BatchRetryResult> {
    try {
      const response = await http.post<{ success: boolean, data: BatchRetryResult, message?: string }>({
        url: `${this.BASE_URL}/batch-retry-by-condition`,
        data: conditions
      })

      if (response && response.success && response.data) {
        return response.data
      }

      throw new Error(response?.message || '按条件批量重推失败')
    } catch (error: any) {
      console.error('Batch retry by condition failed:', error)
      throw new Error(error.message || '按条件批量重推失败')
    }
  }

  /**
   * 获取原始回调统计信息
   */
  static async getRawCallbackStatistics(params: RawCallbackStatisticsParams = {}): Promise<RawCallbackStatistics> {
    try {
      const response = await http.get<{ success: boolean, data: RawCallbackStatistics }>({
        url: `${this.BASE_URL}/statistics`,
        params: {
          start_time: params.start_time || '',
          end_time: params.end_time || '',
          provider: params.provider || '',
          event_type: params.event_type || '',
          _t: Date.now()
        }
      })

      if (response && response.success && response.data) {
        return response.data
      }

      throw new Error('获取原始回调统计失败')
    } catch (error: any) {
      console.error('Get raw callback statistics failed:', error)
      throw new Error(error.message || '获取原始回调统计失败')
    }
  }

  /**
   * 导出原始回调记录
   */
  static async exportRawCallbackRecords(params: RawCallbackListParams = {}): Promise<Blob> {
    try {
      const response = await http.get<Blob>({
        url: `${this.BASE_URL}/export`,
        params: {
          provider: params.provider || '',
          event_type: params.event_type || '',
          order_no: params.order_no || '',
          tracking_no: params.tracking_no || '',
          work_order_id: params.work_order_id || '',  // 🔥 新增：工单ID参数
          start_time: params.start_time || '',
          end_time: params.end_time || '',
          status: params.status || '',
          format: 'csv'
        },
        responseType: 'blob'
      })

      return response
    } catch (error: any) {
      console.error('Export raw callback records failed:', error)
      throw new Error(error.message || '导出原始回调记录失败')
    }
  }

  /**
   * 获取供应商列表
   */
  static getProviderOptions() {
    return [
      { label: '菜鸟', value: 'cainiao' },
      { label: '快递100', value: 'kuaidi100' },
      { label: '易达', value: 'yida' },
      { label: '云通', value: 'yuntong' },
      { label: '快递鸟', value: 'kuaidiniao' }
    ]
  }

  /**
   * 获取事件类型选项（根据供应商）
   */
  static getEventTypeOptions(provider?: string) {
    const eventTypes: { [key: string]: { label: string, value: string }[] } = {
      cainiao: [
        { label: '订单创建', value: 'CREATE_ORDER' },
        { label: '分配运力', value: 'SEEK_DELIVERY_SUCCESS' },
        { label: '已取件', value: 'GOT_SUCCESS' },
        { label: '订单完结', value: 'FINISH_ORDER' },
        { label: '已揽件', value: 'ACCEPT' },
        { label: '运输中', value: 'TRANSPORT' },
        { label: '派送中', value: 'DELIVERING' },
        { label: '已签收', value: 'SIGN' },
        { label: '异常', value: 'FAILED' },
        { label: '拒签', value: 'REJECT' }
      ],
      kuaidi100: [
        { label: '状态更新', value: 'status_update' },
        { label: '轨迹推送', value: 'track_push' }
      ],
      yida: [
        { label: '推送类型1', value: 'push_type_1' },
        { label: '推送类型2', value: 'push_type_2' },
        { label: '推送类型3', value: 'push_type_3' }
      ],
      yuntong: [
        { label: '状态1', value: 'state_1' },
        { label: '状态2', value: 'state_2' },
        { label: '状态3', value: 'state_3' }
      ],
      kuaidiniao: [
        { label: '成功', value: 'success' },
        { label: '失败', value: 'failed' }
      ]
    }

    if (provider && eventTypes[provider]) {
      return eventTypes[provider]
    }

    // 返回所有事件类型
    const allEventTypes: { label: string, value: string }[] = []
    Object.values(eventTypes).forEach(types => {
      allEventTypes.push(...types)
    })
    return allEventTypes
  }

  /**
   * 获取状态选项
   */
  static getStatusOptions() {
    return [
      { label: '成功', value: 'success' },
      { label: '失败', value: 'failed' },
      { label: '处理中', value: 'pending' }
    ]
  }
}

export default RawCallbackManagementService
